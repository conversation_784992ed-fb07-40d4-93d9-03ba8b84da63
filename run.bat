@echo off
setlocal enabledelayedexpansion

echo Starting MBMonitor External Job...

set MAIN_JAR=main\target\external-job-1.0-SNAPSHOT.jar
set LIB_DIR=main\target\lib

if not exist "%MAIN_JAR%" (
    echo Error: JAR file not found: %MAIN_JAR%
    echo Please run: mvn clean package -DskipTests
    pause
    exit /b 1
)

if not exist "%LIB_DIR%" (
    echo Error: Dependencies directory not found: %LIB_DIR%
    echo Please run: mvn clean package -DskipTests
    pause
    exit /b 1
)

set CLASSPATH=%MAIN_JAR%
for %%i in (%LIB_DIR%\*.jar) do (
    set CLASSPATH=!CLASSPATH!;%%i
)

echo Running with classpath: %CLASSPATH%
echo.

java -cp "%CLASSPATH%" vn.com.mbbank.kanban.mbmonitor.external.job.Main %*

if %ERRORLEVEL% neq 0 (
    echo.
    echo Application exited with error code: %ERRORLEVEL%
    pause
)
