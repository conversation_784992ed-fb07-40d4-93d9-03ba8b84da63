@echo off
setlocal enabledelayedexpansion

echo Starting MBMonitor External Job...

set MAIN_JAR=main\target\external-job-1.0-SNAPSHOT.jar
set M2_REPO=%USERPROFILE%\.m2\repository

if not exist "%MAIN_JAR%" (
    echo Error: JAR file not found: %MAIN_JAR%
    echo Please run: mvn clean package -DskipTests
    pause
    exit /b 1
)

echo Building classpath from Maven repository...
set CLASSPATH=%MAIN_JAR%

REM Add common Spring Boot dependencies
set CLASSPATH=%CLASSPATH%;%M2_REPO%\org\springframework\boot\spring-boot-starter\3.3.10\spring-boot-starter-3.3.10.jar
set CLASSPATH=%CLASSPATH%;%M2_REPO%\org\springframework\boot\spring-boot\3.3.10\spring-boot-3.3.10.jar
set CLASSPATH=%CLASSPATH%;%M2_REPO%\org\springframework\spring-context\6.1.13\spring-context-6.1.13.jar
set CLASSPATH=%CLASSPATH%;%M2_REPO%\org\springframework\spring-core\6.1.13\spring-core-6.1.13.jar
set CLASSPATH=%CLASSPATH%;%M2_REPO%\org\springframework\spring-beans\6.1.13\spring-beans-6.1.13.jar

echo Running application...
echo Main JAR: %MAIN_JAR%
echo.

java -cp "%CLASSPATH%" vn.com.mbbank.kanban.mbmonitor.external.job.Main %*

if %ERRORLEVEL% neq 0 (
    echo.
    echo Application exited with error code: %ERRORLEVEL%
    echo.
    echo If you get ClassNotFoundException, try running:
    echo mvn exec:java -Dexec.mainClass="vn.com.mbbank.kanban.mbmonitor.external.job.Main" -pl main
    pause
)
