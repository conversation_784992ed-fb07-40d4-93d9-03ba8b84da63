package vn.com.mbbank.kanban.mbmonitor.common.utils.export;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AttributeInfoDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;

/**
 * CsvExporter for exporting data to CSV in-memory for S3 multipart upload.
 */
public class CsvExporter implements FileExporter {
  private static final String UTF8_BOM = "\uFEFF";

  private List<AttributeInfoDto> sortedAttributes;


  @Override
  public byte[] init(ExportFileDto exportFileDto) throws IOException {
    this.sortedAttributes = exportFileDto.getAttributes().stream()
        .sorted(Comparator.comparingLong(AttributeInfoDto::getPosition))
        .collect(Collectors.toList());

    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    try (OutputStreamWriter writer = new OutputStreamWriter(baos, StandardCharsets.UTF_8);
         CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT)) {
      writer.write(UTF8_BOM);
      printerTitle(exportFileDto.getTitle(), csvPrinter);
      printerHeader(sortedAttributes, csvPrinter);
      csvPrinter.flush();
    }
    return baos.toByteArray();
  }

  @Override
  public <T> byte[] writeBatch(List<T> batchData, ExportFileDto exportFileDto) throws IOException {
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    try (OutputStreamWriter writer = new OutputStreamWriter(baos, StandardCharsets.UTF_8);
         CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT)) {
      var transformedData = ExcelUtils.transformDataExport(batchData, sortedAttributes);
      for (List<AttributeInfoDto> rowData : transformedData) {
        printerRow(rowData, csvPrinter);
      }
      csvPrinter.flush();
    }
    return baos.toByteArray();
  }

  @Override
  public byte[] close() {
    // CSV does not usually require a footer
    return new byte[0];
  }

  private void printerTitle(List<String> titles, CSVPrinter csvPrinter) throws IOException {
    if (Objects.nonNull(titles) && !titles.isEmpty()) {
      for (String title : titles) {
        csvPrinter.printRecord(title);
      }
      csvPrinter.println();
    }
  }

  private void printerHeader(List<AttributeInfoDto> attributes, CSVPrinter csvPrinter) throws IOException {
    for (AttributeInfoDto attr : attributes) {
      csvPrinter.print(attr.getAttributeName());
    }
    csvPrinter.println();
  }

  private void printerRow(List<AttributeInfoDto> rowData, CSVPrinter csvPrinter) throws IOException {
    for (AttributeInfoDto attr : rowData) {
      csvPrinter.print(attr.getValue());
    }
    csvPrinter.println();
  }
}
