package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.exceptions.BusinessRuntimeException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.core.utils.KanbanStringFormatter;
import vn.com.mbbank.kanban.mbmonitor.common.configs.QueryHikariDataSourceConfig;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertPriorityConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SqlExecutionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseThresholdConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.AlertBaseModelToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.DatabaseConnectionRequestToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonDatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRawAlertService;
import vn.com.mbbank.kanban.mbmonitor.common.services.DatabaseQueryService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.DatabaseThresholdRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.DatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.DatabaseThresholdService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FilterAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MaintenanceTimeConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 17/4/2025
 */
@RequiredArgsConstructor
@Service
public class DatabaseThresholdServiceImpl extends BaseServiceImpl<DatabaseThresholdConfigEntity, String>
    implements DatabaseThresholdService {
  private final DatabaseThresholdRepository databaseThresholdRepository;
  private final DatabaseConnectionService databaseConnectionService;
  private final CommonDatabaseConnectionService commonDatabaseConnectionService;
  private final DatabaseQueryService superiorsService;
  private final AlertService alertService;
  private final CommonRawAlertService commonRawAlertService;
  private final MaintenanceTimeConfigService maintenanceTimeConfigService;
  private final FilterAlertConfigService filterAlertConfigService;
  private final ModifyAlertConfigService modifyAlertConfigService;
  private final QueryHikariDataSourceConfig queryHikariDataSourceConfig;
  private final AlertPriorityConfigService alertPriorityConfigService;
  private final SysLogKafkaProducerService sysLogKafkaProducerService;
  final Logger logger = LoggerFactory.getLogger(DatabaseThresholdServiceImpl.class);

  @Override
  protected JpaCommonRepository<DatabaseThresholdConfigEntity, String> getRepository() {
    return databaseThresholdRepository;
  }


  @Override
  @Transactional(rollbackFor = {Exception.class})
  public void collect(String id) throws BusinessException {
    var config = databaseThresholdRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.DATABASE_COLLECT_IS_NOT_EXISTS));

    if (!config.getActive()) {
      throw new BusinessException(ErrorCode.DATABASE_COLLECT_IS_INACTIVE);
    }
    SqlExecutionModel sqlExecutionModel = executeSql(config);
    Long value = getCountValue(sqlExecutionModel);
    if (!checkCondition(value, config)) {
      return;
    }
    var alerts = createAlerts(config, value, config.getConditionValue());
    if (!KanbanCommonUtil.isEmpty(alerts)) {
      collectAlerts(alerts);
    }

  }

  @Override
  public List<DatabaseThresholdConfigEntity> findAllByActiveTrue() {
    return databaseThresholdRepository.findAllByActiveTrue();
  }



  SqlExecutionModel executeSql(DatabaseThresholdConfigEntity config)
      throws BusinessException {
    DatabaseConnectionEntity connectionEntity =
        Optional.ofNullable(databaseConnectionService.findById(config.getDatabaseConnectionId()))
            .orElseThrow(() -> new BusinessException(
                ErrorCode.DATABASE_CONNECTION_NOT_FOUND));
    if (!connectionEntity.getIsActive()) {
      throw new BusinessException(ErrorCode.DATABASE_COLLECT_CONNECTION_INACTIVE);
    }
    DatabaseConnectionRequest request =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.map(connectionEntity);
    request.setId(null);
    request.setPassword(KanbanEncryptorUtils.decrypt(request.getPassword()));
    //check connection
    try {
      databaseConnectionService.checkConnection(request);
    } catch (BusinessException ex) {
      sysLogKafkaProducerService.send(LogActionEnum.DATABASE_THRESHOLD_ERROR, config.getName(), ex.getMessage());
      throw ex;
    }
    // create connection
    HikariConfig dbConfig = commonDatabaseConnectionService.createHikariConfig(request);
    try {
      HikariDataSource dataSource = queryHikariDataSourceConfig.getDataSource(
          dbConfig.getJdbcUrl());
      // get data example
      SqlExecutionResponse sqlExecutionResponse =
          superiorsService.executeQuery(dataSource.getConnection(), config.getSqlCommand(), true);
      return new SqlExecutionModel().setListColumns(sqlExecutionResponse.getListColumns())
          .setListDataMappings(sqlExecutionResponse.getListDataMappings());
    } catch (RuntimeException | SQLException | BusinessException ex) {
      logger.error(ex.getMessage(), ex);
      sysLogKafkaProducerService.send(LogActionEnum.DATABASE_THRESHOLD_ERROR, config.getName(), ex.getMessage());
      throw new BusinessRuntimeException(ErrorCode.DATABASE_CONNECT_FALSE);
    }
  }

  @Override
  public List<AlertBaseModel> filterAlerts(List<AlertBaseModel> alerts) {
    return filterAlertConfigService.updateAlertsForFilter(alerts);
  }

  @Override
  public List<AlertBaseModel> modifyAlerts(List<AlertBaseModel> alerts) {

    return modifyAlertConfigService.updateAlertsForModify(alerts);
  }

  @Override
  public List<AlertBaseModel> maintenanceAlerts(List<AlertBaseModel> alerts) {
    return maintenanceTimeConfigService.updateAlertsForMaintenance(alerts);
  }


  @Override
  public List<AlertBaseModel> saveAlerts(List<AlertBaseModel> alerts) {
    var datas = alertService.saveAll(AlertBaseModelToEntityMapper.INSTANCE.mapTo(alerts));
    return AlertBaseModelToEntityMapper.INSTANCE.map(datas);
  }

  @Override
  public List<AlertBaseModel> collectFilters(List<AlertBaseModel> alerts) {
    return alerts.stream().filter(AlertBaseModel::getIsValid).toList();
  }

  boolean checkCondition(Long value, DatabaseThresholdConfigEntity config) {
    var operator = config.getConditionOperator();
    Long conditionValue = config.getConditionValue();
    return switch (operator) {
      case GREATER_THAN -> value > conditionValue;
      case GREATER_THAN_OR_EQUAL -> value >= conditionValue;
      case EQUAL -> value.equals(conditionValue);
      case LESS_THAN -> value < conditionValue;
      case LESS_THAN_OR_EQUAL -> value <= conditionValue;
    };
  }

  List<AlertBaseModel> createAlerts(DatabaseThresholdConfigEntity config, Long value, Long conditionValue) {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setServiceId(config.getServiceId());
    alertBaseModel.setApplicationId(config.getApplicationId());
    alertBaseModel.setRecipient(config.getRecipient());

    KanbanStringFormatter formatter = KanbanStringFormatter.newBuilder()
        .regexReplace("@count", value.toString())
        .regexReplace("@conditionValue", conditionValue.toString())
        .build();

    String content = formatter.format(config.getContent()).toString();
    if (KanbanCommonUtil.isEmpty(content)) {
      return null;
    }
    var priority = alertPriorityConfigService.findById(config.getPriorityId());

    alertBaseModel.setContent(content);
    alertBaseModel.setContentRaw(content);
    alertBaseModel.setStatus(AlertStatusEnum.NEW);
    alertBaseModel.setAlertGroupId(0L);
    alertBaseModel.setSource(AlertSourceTypeEnum.EMAIL);
    alertBaseModel.setPriorityRaw(KanbanCommonUtil.isEmpty(priority)
        ? AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_NAME
        : priority.getName());

    return commonRawAlertService.createRawData(List.of(alertBaseModel), AlertSourceTypeEnum.DATABASE_THRESHOLD);
  }

  Long getCountValue(SqlExecutionModel model) throws BusinessException {
    return Optional.ofNullable(model.getListDataMappings())
        .flatMap(list -> list.stream().findFirst())
        .map(SqlExecutionResponse.SqlDataMapping::getListSqlMappingColumnDatas)
        .flatMap(list -> list.stream().findFirst())
        .map(SqlExecutionResponse.SqlMappingColumnData::getValue)
        .map(Long::valueOf)
        .orElseThrow(() -> new BusinessException(ErrorCode.DATABASE_THRESHOLD_SQL_RESULT_IS_EMPTY));
  }

}