package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForUser;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.FileStorageRepository;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

public class FileStorageServiceImplTest {

  @TempDir
  Path tempDir;
  @Mock
  private FileStorageRepository fileStorageRepository;
  @InjectMocks
  private FileStorageServiceImpl fileStorageService;

  @BeforeEach
  void setup() {
    MockitoAnnotations.openMocks(this);
  }

  @TestForUser
  void registerFile_success() throws Exception {
    Path tempFile = Files.createFile(tempDir.resolve("test.txt"));

    when(fileStorageRepository.save(any())).thenAnswer(i -> i.getArgument(0));

    FileStorageEntity result = fileStorageService.registerFile(
        tempFile.toString(), "JobA", "123");

    assertEquals(tempFile.toString(), result.getPath());
    assertEquals("JobA", result.getDependencyName());
    assertEquals("123", result.getDependencyId());
  }

  @TestForUser
  void registerFile_error_whenFileNotExist() {
    String path = tempDir.resolve("not_found.txt").toString();

    assertThrows(BusinessException.class, () ->
        fileStorageService.registerFile(path, "JobA", "123")
    );
  }

  @TestForUser
  void findByIdIn_success() {
    List<FileStorageEntity> mockList = List.of(new FileStorageEntity());
    when(fileStorageRepository.findByIdIn(List.of(1L, 2L))).thenReturn(mockList);

    List<FileStorageEntity> result = fileStorageService.findByIdIn(List.of(1L, 2L));
    assertEquals(1, result.size());
  }

  @TestForUser
  void findByIdNotInAndDependencyName_success() {
    List<FileStorageEntity> mockList = List.of(new FileStorageEntity());
    when(fileStorageRepository.findAllByIdNotInAndDependencyName(List.of(1L), "JobA"))
        .thenReturn(mockList);

    List<FileStorageEntity> result = fileStorageService.findByIdNotInAndDependencyName(List.of(1L), "JobA");
    assertEquals(1, result.size());
  }

  @TestForUser
  void deleteExpiredFile_success() throws IOException {
    Path fileToDelete = Files.createFile(tempDir.resolve("file.txt"));
    Path expiredFile = Files.createFile(tempDir.resolve("expired.txt"));
    expiredFile.toFile().setLastModified(System.currentTimeMillis() - (9 * 24 * 60 * 60 * 1000L));

    fileStorageService.deleteExpiredFile(tempDir.toString(), List.of(fileToDelete.toString()));

    assertFalse(Files.exists(fileToDelete));
    assertFalse(Files.exists(expiredFile));
  }

  @TestForUser
  void deleteExpiredFile_ignoreNotFound() {
    String path = tempDir.resolve("missing.txt").toString();

    assertDoesNotThrow(() ->
        fileStorageService.deleteExpiredFile(tempDir.toString(), List.of(path))
    );
  }

  @TestForUser
  void deleteExpiredFile_keepRecentFile() throws IOException {
    Path recentFile = Files.createFile(tempDir.resolve("recent.txt"));
    recentFile.toFile().setLastModified(System.currentTimeMillis());

    fileStorageService.deleteExpiredFile(tempDir.toString(), List.of());

    assertTrue(Files.exists(recentFile));
  }

  @TestForDev
  void deleteExpiredFile_error_IOExceptionWhendeleteByListPath() {
    String filePathStr = "dummy.txt";
    Path filePath = Paths.get(filePathStr).toAbsolutePath().normalize();

    try (MockedStatic<Files> mockedFiles = mockStatic(Files.class)) {
      mockedFiles.when(() -> Files.exists(filePath)).thenReturn(true);
      mockedFiles.when(() -> Files.delete(filePath))
          .thenThrow(new IOException("Simulated delete error"));

      assertDoesNotThrow(() ->
          fileStorageService.deleteExpiredFile("dummyFolder", List.of(filePathStr))
      );
    }
  }

  @Test
  void deleteExpiredFile_error_IOExceptionWhendeleteByListPath2() {
    String filePathStr = tempDir.resolve("dummy.txt").toString();
    Path filePath = Paths.get(filePathStr).toAbsolutePath().normalize();

    try (MockedStatic<Files> mockedFiles = mockStatic(Files.class)) {
      mockedFiles.when(() -> Files.exists(filePath)).thenReturn(true);
      mockedFiles.when(() -> Files.delete(filePath))
          .thenThrow(new IOException("Simulated delete error"));

      assertDoesNotThrow(() ->
          fileStorageService.deleteExpiredFile("dummyFolder", List.of(filePathStr))
      );
    }
  }

  @Test
  void deleteExpiredFile_error_IOExceptionWhenWalkingDirectory() {
    String folder = tempDir.resolve("dummyFolder").toString();
    Path baseDir = Paths.get(folder).toAbsolutePath().normalize();

    try (MockedStatic<Files> mockedFiles = Mockito.mockStatic(Files.class)) {
      mockedFiles.when(() -> Files.walk(baseDir))
          .thenThrow(new IOException("Simulated walk error"));

      assertDoesNotThrow(() ->
          fileStorageService.deleteExpiredFile(folder, List.of())
      );
    }
  }

  @TestForDev
  void getRepository_success() {
    assertEquals(fileStorageRepository, fileStorageService.getRepository());
  }
}