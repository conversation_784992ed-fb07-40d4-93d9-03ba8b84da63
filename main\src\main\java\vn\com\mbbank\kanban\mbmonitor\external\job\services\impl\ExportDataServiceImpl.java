package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.MessageFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.constants.NotificationConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportDataMessageModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileAlertRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileApplicationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileServiceRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExportDataEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileSourceEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.PushNotificationService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.ExportDataRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ExportDataService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FileStorageService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ServiceService;
import vn.com.mbbank.kanban.mbmonitor.external.job.utils.exports.TriFunction;


@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExportDataServiceImpl extends BaseServiceImpl<ExportDataEntity, String>
    implements ExportDataService {

  static final Logger logger = LoggerFactory.getLogger(ExportDataServiceImpl.class);
  final ExportDataRepository exportDataRepository;
  final AlertService alertService;
  final ServiceService serviceService;
  final ApplicationService applicationService;
  final FileStorageService fileStorageService;
  final PushNotificationService pushNotificationService;

  @Value("${mbmonitor.file-upload.dir}")
  String uploadDir;

  @Value("${mbmonitor.file-upload.expired-days}")
  Integer fileExpiredDays;

  @Override
  protected JpaCommonRepository<ExportDataEntity, String> getRepository() {
    return exportDataRepository;
  }


  @Override
  public void exportFile(String id, ExportFileAlertRequest request) {
    processExportFile(id, request, alertService::exportFile);
  }

  @Override
  public void exportFile(String id, ExportFileServiceRequest request) {
    processExportFile(id, request, serviceService::exportFile);
  }

  @Override
  public void exportFile(String id, ExportFileApplicationRequest request) {
    processExportFile(id, request, applicationService::exportFile);
  }

  @Override
  @Transactional
  public void deleteExpiredFile() {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, -fileExpiredDays);
    Date expirationThreshold = cal.getTime();
    List<ExportDataEntity> exportDataEntities = exportDataRepository
        .findAllByCreatedDateBeforeAndStatus(expirationThreshold, ExportFileStatusEnum.DONE);
    exportDataRepository.updateStatusByIdIn(
        exportDataEntities.stream().map(ExportDataEntity::getId).toList(),
        ExportFileStatusEnum.DELETED.name());
    List<FileStorageEntity> fileStorageEntities = fileStorageService.findByIdIn(
        exportDataEntities.stream().map(ExportDataEntity::getFileStorageId).toList());
    fileStorageService.deleteByIdIn(
        fileStorageEntities.stream().map(FileStorageEntity::getId).toList());
    String rootFolder = Paths.get(uploadDir, CommonConstants.MODULE_EXPORT).toString();
    fileStorageService.deleteExpiredFile(rootFolder,
        fileStorageEntities.stream().map(FileStorageEntity::getPath).toList());
  }

  private String generateFilePath(String dependencyId, ExportFileTypeEnum extension) {
    String timestamp = String.valueOf(System.currentTimeMillis());
    String fileExtension = extension.name().toLowerCase();
    String fileName = String.format("%s.%s", timestamp, fileExtension);
    return String.format("%s/%s/%s", CommonConstants.MODULE_EXPORT, dependencyId, fileName);
  }

  protected <T extends ExportFileRequest> void processExportFile(
      String id,
      T request,
      TriFunction<T, String, String, FileStorageEntity> exportFunction) {
    try {
      ExportDataEntity exportData = exportDataRepository.findById(id)
          .orElseThrow(() -> new BusinessException(ErrorCode.EXPORT_DATA_NOT_FOUND));
      String filePath = generateFilePath(exportData.getExportedBy(), request.getExportDataModel().getExtension());
      FileStorageEntity fileStorageEntity = exportFunction.apply(request, exportData.getExportedBy(), filePath);
      if (Objects.isNull(fileStorageEntity.getId())) {
        throw new BusinessException(ErrorCode.FILE_IS_NOT_VALID);
      }
      exportDataRepository.updateStatusAndFileStorageId(
          id,
          ExportFileStatusEnum.DONE.name(),
          fileStorageEntity.getId());
      NotificationRequest notification = NotificationRequest.builder()
          .title(NotificationConstants.EXPORT_DATA_TITLE)
          .content(MessageFormat.format(NotificationConstants.EXPORT_DATA_MESSAGE, exportData.getFileName()))
          .type(NotificationTypeEnum.INFO)
          .userName(exportData.getExportedBy())
          .sourceId(exportData.getId())
          .sourceType(NotificationSourceTypeEnum.EXPORT_DATA)
          .build();
      pushNotificationService.push(notification);
    } catch (Exception e) {
      logger.error("Error when export data " + e.getMessage());
      exportDataRepository.updateStatusById(id, ExportFileStatusEnum.FAILED.name());
    }
  }


  @Override
  public boolean isKafkaMultipleGroup() {
    return false;
  }

  @Override
  public <T> void kafkaExecute(BaseKafkaModel<T> data) throws Exception {
    logger.error(MessageFormat.format("Start export: {0}",
        DateUtils.formatDate(new Date())));
    ExportDataMessageModel exportDataModel = KanbanMapperUtils.jsonToObject(data.getValue().toString(),
        ExportDataMessageModel.class);
    if (ExportFileSourceEnum.ALERT.equals(exportDataModel.getSource())) {
      ExportFileAlertRequest request =
          KanbanMapperUtils.objectToObject(exportDataModel.getRequest(),
              ExportFileAlertRequest.class);
      exportFile(exportDataModel.getId(), request);
    } else if (ExportFileSourceEnum.SERVICE.equals(exportDataModel.getSource())) {
      ExportFileServiceRequest request =
          KanbanMapperUtils.objectToObject(exportDataModel.getRequest(),
              ExportFileServiceRequest.class);
      exportFile(exportDataModel.getId(), request);
    } else if (ExportFileSourceEnum.APPLICATION.equals(exportDataModel.getSource())) {
      ExportFileApplicationRequest request =
          KanbanMapperUtils.objectToObject(exportDataModel.getRequest(),
              ExportFileApplicationRequest.class);
      exportFile(exportDataModel.getId(), request);
    }
    logger.error(MessageFormat.format("End export: {0}",
        DateUtils.formatDate(new Date())));
  }


  @Override
  public KafkaTypeEnum getKafkaType() {
    return KafkaTypeEnum.EXPORT_DATA;
  }
}
