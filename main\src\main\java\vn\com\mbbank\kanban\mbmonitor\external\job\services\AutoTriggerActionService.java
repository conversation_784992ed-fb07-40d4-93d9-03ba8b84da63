package vn.com.mbbank.kanban.mbmonitor.external.job.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;

/**
 * AutoTriggerActionService.
 */
public interface AutoTriggerActionService extends BaseService<AutoTriggerActionConfigEntity, String> {

  /**
   * trigger job.
   *
   * @param singleAlerts for trigger
   * @param groupIds      for find alerts to trigger
   */
  void triggerJob(List<AlertEntity> singleAlerts, List<Long> groupIds);
}
