// Test file để kiểm tra logic export
// Các vấn đề đã được xác định:

// 1. ExportFileProcessor.java - Dòng 67:
// TRƯỚC: while (hasMoreData && currentOffset < fileSize && currentOffset % batchSize == 0)
// SAU: while (hasMoreData && currentOffset < fileSize)
// 
// Vấn đề: Điều kiện currentOffset % batchSize == 0 chỉ đúng ở lần đầu tiên.
// Sau khi currentOffset tăng lên, nó sẽ không còn chia hết cho batchSize nữa.

// 2. AlertServiceImpl.java - Dòng 146:
// TRƯỚC: exporter.writeBatch(alerts, fileDto); // Không ghi vào stream
// SAU: byte[] batchBytes = exporter.writeBatch(alerts, fileDto);
//      if (batchBytes.length > 0) {
//        String etag = s3FileService.uploadPart(filePath, uploadId, partNumber++, batchBytes);
//        etags.add(etag);
//      }
//
// Vấn đề: Method writeBatch() trả về byte[] nhưng không được ghi vào S3.

// 3. ExportFileProcessor.java - Thiếu init() và close():
// TRƯỚC: Không có header và footer
// SAU: Thêm exporter.init() và exporter.close()
//
// Vấn đề: File không có header (tiêu đề cột) và footer.

// Tóm tắt các thay đổi đã thực hiện:
// 1. Sửa logic vòng lặp trong ExportFileProcessor
// 2. Thêm init() và close() vào ExportFileProcessor  
// 3. Sửa AlertServiceImpl để sử dụng ExportFileProcessor thống nhất
// 4. Đảm bảo dữ liệu được ghi vào S3 đúng cách

// Kết quả mong đợi:
// - File export sẽ có header (tiêu đề cột)
// - File export sẽ có dữ liệu thực tế
// - File export sẽ có footer (nếu cần)
// - Logic export thống nhất giữa Alert, Service, và Application
