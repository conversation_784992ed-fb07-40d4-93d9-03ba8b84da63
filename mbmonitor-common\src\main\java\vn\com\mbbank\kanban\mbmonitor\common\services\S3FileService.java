package vn.com.mbbank.kanban.mbmonitor.common.services;

import software.amazon.awssdk.services.s3.S3Client;

import java.io.IOException;
import java.util.List;

/**
 * Common S3 service for basic file operations.
 */
public interface S3FileService {

  /**
   * Uploads a file to the S3 bucket.
   *
   * @param key         the object key (path in bucket)
   * @param content     the file content as byte array
   * @param contentType the MIME type of the file
   */
  void upload(String key, byte[] content, String contentType);

  /**
   * Downloads a file from S3.
   *
   * @param key the object key
   * @return file content as byte array
   */
  byte[] download(String key);

  /**
   * Deletes a file from S3.
   *
   * @param key the object key
   */
  void delete(String key);

  /**
   * Checks if the file exists in S3.
   *
   * @param key the object key
   * @return true if the file exists
   */
  boolean exists(String key);
  /**
   * Returns the current configured S3Client instance.
   *
   * @return the S3Client
   */
  S3Client getS3Client();

  /**
   * Returns the configured bucket name.
   *
   * @return the bucket name
   */
  String getBucketName();

  /**
   * Begins a multipart upload and returns the upload ID.
   *
   * @param key         the file path (key)
   * @param contentType the MIME type of the file
   * @return upload ID for multipart upload
   */
  String beginMultipartUpload(String key, String contentType);

  /**
   * Uploads a part of a multipart upload.
   *
   * @param key        the file path (key)
   * @param uploadId   the upload ID
   * @param partNumber the part number (starting from 1)
   * @param partBytes  the byte content of the part
   * @return the ETag of the uploaded part
   */
  String uploadPart(String key, String uploadId, int partNumber, byte[] partBytes);

  /**
   * Completes a multipart upload.
   *
   * @param key      the file path (key)
   * @param uploadId the upload ID
   * @param etags    the list of ETags for all uploaded parts
   */
  void completeMultipartUpload(String key, String uploadId, List<String> etags);

  /**
   * Aborts a multipart upload.
   *
   * @param key      the file path (key)
   * @param uploadId the upload ID
   */
  void abortMultipartUpload(String key, String uploadId);
}
