package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.exceptions.BusinessRuntimeException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.mbmonitor.common.configs.QueryHikariDataSourceConfig;
import vn.com.mbbank.kanban.mbmonitor.common.constants.DatabaseCollectConstants;
import vn.com.mbbank.kanban.mbmonitor.common.constants.JobNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SqlExecutionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectTempEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.JobHistoryDetailEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.JobHistoryEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConfigCollectMapTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.JobHistoryStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.AlertBaseModelToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.DatabaseConnectionRequestToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonDatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRawAlertService;
import vn.com.mbbank.kanban.mbmonitor.common.services.DatabaseQueryService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DatabaseCollectUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.dtos.models.DatabaseCollectAlertModal;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.DatabaseCollectRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.DatabaseCollectService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.DatabaseCollectTempService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.DatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FilterAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.JobHistoryDetailService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.JobHistoryService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MaintenanceTimeConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/12/2024
 */
@RequiredArgsConstructor
@Service
public class DatabaseCollectServiceImpl extends BaseServiceImpl<DatabaseCollectEntity, Long>
    implements
    DatabaseCollectService {
  private final DatabaseCollectRepository databaseCollectRepository;
  private final DatabaseConnectionService databaseConnectionService;
  private final CommonDatabaseConnectionService commonDatabaseConnectionService;
  private final DatabaseQueryService superiorsService;
  private final DatabaseCollectTempService databaseCollectTempService;
  private final AlertService alertService;
  private final CommonRawAlertService commonRawAlertService;
  private final JobHistoryService jobHistoryService;
  private final JobHistoryDetailService jobHistoryDetailService;
  private final MaintenanceTimeConfigService maintenanceTimeConfigService;
  private final FilterAlertConfigService filterAlertConfigService;
  private final ModifyAlertConfigService modifyAlertConfigService;
  private final QueryHikariDataSourceConfig queryHikariDataSourceConfig;
  final Logger logger = LoggerFactory.getLogger(DatabaseCollectServiceImpl.class);
  @Value("${monitor.sql.query.row.max:500}")
  private Integer rowLimit;

  @Override
  protected JpaCommonRepository<DatabaseCollectEntity, Long> getRepository() {
    return databaseCollectRepository;
  }

  private static String MAP_COLLECT_ID_KEY = "ID";

  @Override
  @Transactional(rollbackFor = {Exception.class})
  public void collect(Long id) throws BusinessException {
    var config = databaseCollectRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.DATABASE_COLLECT_IS_NOT_EXISTS));

    // stop job if it's inactive
    if (!config.getIsActive()) {
      throw new BusinessException(ErrorCode.DATABASE_COLLECT_IS_INACTIVE);
    }

    var connection = databaseConnectionService.findById(config.getConnectionId());
    if (connection == null || !connection.getIsActive()) {
      throw new BusinessException(ErrorCode.DATABASE_COLLECT_CONNECTION_INACTIVE);
    }
    // check data from temp
    var lstDataTemp = databaseCollectTempService.findAllByDatabaseCollectId(config.getId());
    /**
     * check temp case temp null -> get create Date from config
     * temp case not null: apply date from temp filter data
     */
    SqlExecutionModel sqlExecutionModel = new SqlExecutionModel();
    if (CollectionUtils.isEmpty(lstDataTemp)) {
      sqlExecutionModel = executeSql(config, new Timestamp(config.getModifiedDate().getTime()));
    } else {
      // when update config then collect data from time update config
      Timestamp timeCollect =
          lstDataTemp.get(0).getAlertCollectDate().getTime() > config.getModifiedDate().getTime()
              ?
              lstDataTemp.get(0).getAlertCollectDate() :
              new Timestamp(config.getModifiedDate().getTime());
      sqlExecutionModel = executeSql(config, timeCollect);
    }
    // save data to alert table
    if (CollectionUtils.isEmpty(sqlExecutionModel.getListDataMappings())) {
      return;
    }

    List<AlertBaseModel> alertBaseModels = new ArrayList<>();
    List<DatabaseCollectTempEntity> alertTempEntities = new ArrayList<>();
    for (var row : sqlExecutionModel.getListDataMappings()) {
      AlertBaseModel alertBaseModel = new AlertBaseModel();
      try {
        var alertInfo = mapperDataToAlertModel(config, row);
        alertBaseModel = alertInfo.getAlertBaseModel();
        boolean isAlertExists = false;
        if (!CollectionUtils.isEmpty(lstDataTemp)) {
          isAlertExists = lstDataTemp.stream()
              .anyMatch(
                  temp -> temp.getAlertCollectId().equalsIgnoreCase(alertInfo.getAlertCollectId()));
        }

        if (!isAlertExists) {
          alertBaseModel.setAlertGroupId(0L);
          DatabaseCollectTempEntity collectTemp = new DatabaseCollectTempEntity();
          collectTemp.setId(id);
          collectTemp.setAlertCollectId(alertInfo.getAlertCollectId());
          collectTemp.setDatabaseCollectId(id);
          collectTemp.setAlertCollectDate(alertInfo.getAlertCollectDate());
          alertTempEntities.add(collectTemp);
        } else {
          alertBaseModel.setIsValid(false);
          alertBaseModel.setDescription(
              "DATA EXIST IN TEMP: " + KanbanCommonUtil.beanToString(alertInfo));
        }
      } catch (BusinessException | RuntimeException ex) {
        alertBaseModel.setIsValid(false);
        alertBaseModel.setDescription(
            ex.getMessage() + ExceptionUtils.getRootCause(ex) + ExceptionUtils.getStackTrace(ex));
        logger.error("Collect data error", ex);
      }
      alertBaseModels.add(alertBaseModel);
    }
    // save alert
    if (!CollectionUtils.isEmpty(alertBaseModels)) {
      // build alert with raw data
      var alertRawsBuild =
          commonRawAlertService.createRawData(alertBaseModels, AlertSourceTypeEnum.DATABASE);
      // collect alerts
      collectAlerts(Map.of(MAP_COLLECT_ID_KEY, id),
          alertRawsBuild);
      if (!KanbanCommonUtil.isEmpty(alertTempEntities)) {
        databaseCollectTempService.deleteAllByDatabaseCollectId(id);
        databaseCollectTempService.saveAll(alertTempEntities);
      }
    }
  }

  @Override
  public List<DatabaseCollectEntity> findAllByIsActiveTrue() {
    return databaseCollectRepository.findAllByIsActiveTrue();
  }

  private DatabaseCollectAlertModal mapperDataToAlertModel(DatabaseCollectEntity config,
                                                           SqlExecutionResponse.SqlDataMapping row)
      throws BusinessException {
    if (CollectionUtils.isEmpty(row.getListSqlMappingColumnDatas())) {
      return DatabaseCollectAlertModal.builder().alertBaseModel(new AlertBaseModel()).build();
    }

    AlertBaseModel alertBaseModel = new AlertBaseModel();
    //mapping data service
    mappingConfigToBaseModel(alertBaseModel, config.getServiceId(), row,
        config.getServiceMapValue(), config.getServiceNameType(), AlertBaseModel::setServiceNameRaw,
        AlertBaseModel::setServiceId);

    //mapping data application
    mappingConfigToBaseModel(alertBaseModel, config.getApplicationId(), row,
        config.getApplicationMapValue(), config.getApplicationType(),
        AlertBaseModel::setApplicationNameRaw,
        AlertBaseModel::setApplicationId);

    //mapping data priority
    mappingConfigToBaseModel(alertBaseModel, config.getPriorityId(), row,
        config.getPriorityMapValue(), config.getPriorityType(),
        AlertBaseModel::setPriorityRaw,
        AlertBaseModel::setAlertPriorityConfigId);

    //mapping data contact
    mappingConfigToBaseModel(alertBaseModel, config.getContactCustomValue(), row,
        config.getContactMapValue(), config.getContactType(),
        AlertBaseModel::setRecipientRaw,
        AlertBaseModel::setRecipient);

    //mapping data content
    mappingConfigToBaseModel(alertBaseModel, config.getAlertMapValue(), row,
        config.getAlertMapValue(), ConfigCollectMapTypeEnum.FROM_SOURCE,
        AlertBaseModel::setContentRaw,
        AlertBaseModel::setContent);

    //create date alert
    var alertCollectDate = getAlertCollectDate(row.getListSqlMappingColumnDatas(), config);
    //alertIdFiled
    String alertCollectId = getAlertCollectId(row.getListSqlMappingColumnDatas(), config);
    return DatabaseCollectAlertModal.builder().alertBaseModel(alertBaseModel)
        .alertCollectId(alertCollectId)
        .alertCollectDate(alertCollectDate).build();

  }

  private SqlExecutionModel executeSql(DatabaseCollectEntity config, Timestamp createDateParam)
      throws BusinessException {
    DatabaseConnectionEntity connectionEntity =
        Optional.ofNullable(databaseConnectionService.findById(config.getConnectionId()))
            .orElseThrow(() -> new BusinessException(
                ErrorCode.DATABASE_CONNECTION_NOT_FOUND));
    DatabaseConnectionRequest request =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.map(connectionEntity);
    request.setId(null);
    request.setPassword(KanbanEncryptorUtils.decrypt(request.getPassword()));
    //check connection
    databaseConnectionService.checkConnection(request);
    // create connection
    HikariConfig dbConfig = commonDatabaseConnectionService.createHikariConfig(request);
    try {
      HikariDataSource dataSource = queryHikariDataSourceConfig.getDataSource(
          dbConfig.getJdbcUrl());
      // get data example
      SqlExecutionResponse sqlExecutionResponse =
          superiorsService.executeQuery(dataSource.getConnection(),
              DatabaseCollectUtils.getQueryWithLimit(config.getSqlCommand(), rowLimit, request.getType()), true, null,
              Map.of(DatabaseCollectConstants.CREATE_DATE_PARAMETER.substring(1),
                  createDateParam));
      return new SqlExecutionModel().setListColumns(sqlExecutionResponse.getListColumns())
          .setListDataMappings(sqlExecutionResponse.getListDataMappings());
    } catch (RuntimeException | SQLException ex) {
      logger.error(ex.getMessage(), ex);
      throw new BusinessRuntimeException(ErrorCode.DATABASE_CONNECT_FALSE);
    }
  }

  private String getAlertCollectId(
      List<SqlExecutionResponse.SqlMappingColumnData> listSqlMappingColumnDatas,
      DatabaseCollectEntity config) throws BusinessException {
    var alertCollectId = listSqlMappingColumnDatas.stream()
        .filter(data -> data.getColumn().equalsIgnoreCase(config.getAlertIdField())).findFirst()
        .orElseThrow(() -> new BusinessException(ErrorCode.DATABASE_COLLECT_COLUMN_DATA_NOT_FOUND,
            config.getAlertIdField()));
    return alertCollectId.getValue();
  }


  private Timestamp getAlertCollectDate(
      List<SqlExecutionResponse.SqlMappingColumnData> listSqlMappingColumnDatas,
      DatabaseCollectEntity config) throws BusinessException {
    var alertCollectDate = listSqlMappingColumnDatas.stream()
        .filter(data -> data.getColumn().equalsIgnoreCase(config.getCreatedDateField())).findFirst()
        .orElseThrow(() -> new BusinessException(ErrorCode.DATABASE_COLLECT_COLUMN_DATA_NOT_FOUND,
            config.getCreatedDateField()));
    return Timestamp.valueOf(alertCollectDate.getValue());
  }

  @Override
  public List<AlertBaseModel> filterAlerts(List<AlertBaseModel> alerts) {
    return filterAlertConfigService.updateAlertsForFilter(alerts);
  }

  @Override
  public List<AlertBaseModel> modifyAlerts(List<AlertBaseModel> alerts) {

    return modifyAlertConfigService.updateAlertsForModify(alerts);
  }

  @Override
  public List<AlertBaseModel> maintenanceAlerts(List<AlertBaseModel> alerts) {
    return maintenanceTimeConfigService.updateAlertsForMaintenance(alerts);
  }


  @Override
  public List<AlertBaseModel> saveAlerts(List<AlertBaseModel> alerts) {
    var datas = alertService.saveAll(AlertBaseModelToEntityMapper.INSTANCE.mapTo(alerts));
    return AlertBaseModelToEntityMapper.INSTANCE.map(datas);
  }

  @Override
  public List<AlertBaseModel> collectFilters(List<AlertBaseModel> alerts) {
    return alerts.stream().filter(obj -> obj.getIsValid()).toList();
  }

  @Override
  public List<AlertBaseModel> collectAlertsEnd(Map<String, Object> config,
                                               List<AlertBaseModel> beforeAlerts,
                                               List<AlertBaseModel> endAlerts) {
    // save log
    JobHistoryEntity jobHistoryEntity = new JobHistoryEntity();
    jobHistoryEntity.setJobType(JobNameConstants.DATABASE_COLLECT);
    jobHistoryEntity.setJobId(
        NumberUtils.toLong(config.getOrDefault(MAP_COLLECT_ID_KEY, "").toString(), 0L));
    jobHistoryEntity.setStartDate(new Date());
    jobHistoryEntity.setTotal(beforeAlerts.stream().count());
    jobHistoryEntity.setSuccess(endAlerts.stream().count());
    jobHistoryEntity = jobHistoryService.save(jobHistoryEntity);
    List<JobHistoryDetailEntity> logDetails = new ArrayList<>();
    for (var alert : beforeAlerts) {
      if (!alert.getIsValid()) {
        JobHistoryDetailEntity jobHistoryDetail = new JobHistoryDetailEntity();
        jobHistoryDetail.setHistoryId(jobHistoryEntity.getId());
        jobHistoryDetail.setStatus(JobHistoryStatusEnum.ERROR);
        jobHistoryDetail.setDescription(KanbanCommonUtil.beanToString(alert));
        logDetails.add(jobHistoryDetail);
      }
    }
    jobHistoryEntity.setEndDate(new Date());
    jobHistoryService.save(jobHistoryEntity);
    jobHistoryDetailService.saveAll(logDetails);
    return endAlerts;
  }

  private <V> void mappingConfigToBaseModel(
      AlertBaseModel data,
      V id,
      SqlExecutionResponse.SqlDataMapping row,
      String columnName, ConfigCollectMapTypeEnum type,
      BiConsumer<AlertBaseModel, String> setterName,
      BiConsumer<AlertBaseModel, ? super V> setterId) {

    if (ConfigCollectMapTypeEnum.FROM_SOURCE.equals(type)) {
      var columnData = row.getListSqlMappingColumnDatas().stream()
          .filter(column -> column.getColumn().equalsIgnoreCase(columnName)).findFirst()
          .orElse(null);
      if (KanbanCommonUtil.isEmpty(columnData)) {
        data.setIsValid(false);
        data.setDescription(
            MessageFormat.format(ErrorCode.DATABASE_COLLECT_COLUMN_DATA_NOT_FOUND.getMessage(),
                columnName));
      }
      setterName.accept(data, columnData.getValue());
    } else {
      setterId.accept(data, id);
    }

  }


}