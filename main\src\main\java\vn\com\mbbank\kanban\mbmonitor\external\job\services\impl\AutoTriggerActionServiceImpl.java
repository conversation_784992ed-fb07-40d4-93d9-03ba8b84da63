package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import com.nimbusds.oauth2.sdk.util.CollectionUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.constants.BeanNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigExecutionMapEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CustomObjectUtils;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AutoTriggerActionConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AutoTriggerActionConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AutoTriggerActionConfigExecutionMapService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AutoTriggerActionService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CustomObjectService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ExecutionService;

@Service
@RequiredArgsConstructor
public class AutoTriggerActionServiceImpl extends BaseServiceImpl<AutoTriggerActionConfigEntity, String>
        implements AutoTriggerActionService {

  private final AutoTriggerActionConfigRepository autoTriggerActionConfigRepository;
  private final CustomObjectService customObjectService;
  private final AlertService alertService;
  private final AutoTriggerActionConfigDependencyService autoTriggerActionConfigDependencyService;
  private final AutoTriggerActionConfigExecutionMapService triggerActionMapService;
  private final ExecutionService executionService;
  @Autowired
  @Qualifier(BeanNameConstants.COMMON_TASK_EXECUTOR)
  Executor commonTaskExecutor;
  private static final Logger logger = LoggerFactory.getLogger(AutoTriggerActionServiceImpl.class);

  @Override
  protected JpaCommonRepository<AutoTriggerActionConfigEntity, String> getRepository() {
    return autoTriggerActionConfigRepository;
  }

  @Async(BeanNameConstants.COMMON_TASK_EXECUTOR)
  @Override
  public void triggerJob(List<AlertEntity> singleAlerts, List<Long> groupIds) {
    var primaryAlerts = alertService.findAlertKeyByGroupIdIn(groupIds);
    if (CollectionUtils.isNotEmpty(singleAlerts)) {
      primaryAlerts.addAll(singleAlerts);
    }
    List<AutoTriggerActionConfigEntity> autoTriggerActionConfigEntities =
            autoTriggerActionConfigRepository.findAllByActiveAndCreatedDate();
    if (CollectionUtils.isEmpty(primaryAlerts) || CollectionUtils.isEmpty(autoTriggerActionConfigEntities)) {
      return;
    }
    List<String> configIds = autoTriggerActionConfigEntities.stream()
            .map(AutoTriggerActionConfigEntity::getId)
            .toList();

    Map<String, List<AutoTriggerActionConfigDependencyEntity>> dependenciesMap =
            autoTriggerActionConfigDependencyService
                    .findAllByAutoTriggerActionIdIn(configIds)
                    .stream()
                    .collect(
                            Collectors.groupingBy(AutoTriggerActionConfigDependencyEntity
                                    ::getAutoTriggerActionConfigId));
    Set<AutoTriggerActionConfigEntity> matchedConfigs = ConcurrentHashMap.newKeySet();
    var customObjects = customObjectService.findAllByDeletedIsFalse();
    for (AlertEntity alertEntity : primaryAlerts) {
      for (AutoTriggerActionConfigEntity config : autoTriggerActionConfigEntities) {
        if (dependenciesMatch(config, alertEntity, dependenciesMap)
                && config.getRuleGroup()
                .check(CustomObjectUtils.getAlertConditionValueMapForTrigger(alertEntity, customObjects))) {
          matchedConfigs.add(config);
          break;
        }
      }
    }

    Map<String, List<String>> configIdToExecutionIds = matchedConfigs.stream()
            .map(config -> Map.entry(
                    config.getId(),
                    triggerActionMapService.findAllByAutoTriggerActionConfigId(config.getId())
                            .stream()
                            .map(AutoTriggerActionConfigExecutionMapEntity::getExecutionId)
                            .toList()
            ))
            .filter(entry -> !entry.getValue().isEmpty())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    Set<String> allExecutionIds = configIdToExecutionIds.values().stream()
            .flatMap(List::stream)
            .collect(Collectors.toSet());

    Map<String, ExecutionEntity> executionMap = executionService.findAllByIdIn(new ArrayList<>(allExecutionIds))
            .stream()
            .collect(Collectors.toMap(ExecutionEntity::getId, Function.identity()));

    Map<String, List<ExecutionEntity>> configIdToExecutions = configIdToExecutionIds.entrySet().stream()
            .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().stream()
                            .map(executionMap::get)
                            .filter(Objects::nonNull)
                            .toList()
            ));
    List<CompletableFuture<Void>> configFutures = matchedConfigs.stream()
            .map(config -> CompletableFuture.runAsync(() -> {
              List<ExecutionEntity> executions = configIdToExecutions.getOrDefault(config.getId(), List.of());

              if (!executions.isEmpty()) {
                List<CompletableFuture<Void>> execFutures = executions.stream()
                        .map(execution -> CompletableFuture.runAsync(() -> {
                          try {
                            executionService.process(execution, config.getName());
                          } catch (Exception e) {
                            logger.error("Error when processing execution {}: {}",
                                    execution.getName(), e.getMessage(), e);
                          }
                        }, commonTaskExecutor).exceptionally(ex -> {
                          logger.error("Unhandled exception in async trigger {} for execution {}: {}",
                                  config.getName(), execution.getName(), ex.getMessage(), ex);
                          return null;
                        }))
                        .toList();

                CompletableFuture.allOf(execFutures.toArray(new CompletableFuture[0])).join();
                config.setLastRun(new Date());
                autoTriggerActionConfigRepository.save(config);
              }
            }, commonTaskExecutor))
            .toList();

    CompletableFuture.allOf(configFutures.toArray(new CompletableFuture[0])).join();
  }

  private boolean dependenciesMatch(AutoTriggerActionConfigEntity config, AlertEntity alert,
                                    Map<String, List<AutoTriggerActionConfigDependencyEntity>> dependenciesMap) {
    List<AutoTriggerActionConfigDependencyEntity> dependencyEntities = dependenciesMap
            .getOrDefault(config.getId(), Collections.emptyList());
    if (KanbanCommonUtil.isEmpty(dependencyEntities)) {
      return true;
    }
    Set<String> dependencyApplications = dependencyEntities.stream()
            .filter(e -> e.getType() == DependencyTypeEnum.APPLICATION)
            .map(AutoTriggerActionConfigDependencyEntity::getDependencyId)
            .collect(Collectors.toSet());

    Set<String> dependencyServices = dependencyEntities.stream()
            .filter(e -> e.getType() == DependencyTypeEnum.SERVICE)
            .map(AutoTriggerActionConfigDependencyEntity::getDependencyId)
            .collect(Collectors.toSet());

    Set<String> dependencyServiceWithAllApp = dependencyEntities.stream()
            .filter(
                    e -> e.getType() == DependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION)
            .map(AutoTriggerActionConfigDependencyEntity::getDependencyId)
            .collect(Collectors.toSet());

    return (dependencyApplications.contains(alert.getApplicationId())
            && dependencyServices.contains(alert.getServiceId()))
            || dependencyServiceWithAllApp.contains(alert.getServiceId());
  }
}
