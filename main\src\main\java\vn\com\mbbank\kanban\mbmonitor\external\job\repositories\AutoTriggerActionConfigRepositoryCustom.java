package vn.com.mbbank.kanban.mbmonitor.external.job.repositories;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;

/**
 * AutoTriggerActionConfigRepositoryCustom interface.
 */
public interface AutoTriggerActionConfigRepositoryCustom {

  /**
   * find all auto trigger action config by active.
   *
   * @return AutoTriggerActionConfigEntity.
   */
  List<AutoTriggerActionConfigEntity> findAllByActiveAndCreatedDate();
}
