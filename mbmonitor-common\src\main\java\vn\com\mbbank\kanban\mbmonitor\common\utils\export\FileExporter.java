package vn.com.mbbank.kanban.mbmonitor.common.utils.export;

import java.io.IOException;
import java.util.List;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;

/**
 * Interface for exporting data into a specific file format, optimized for S3 multipart upload.
 */
public interface FileExporter {

  /**
   * Export full data to a stream (e.g., for direct download).
   *
   * @param data          data list to export
   * @param exportFileDto metadata for export
   * @param <T>           data type
   * @return StreamingResponseBody stream of file content
   * @throws IOException if export fails
   */
  <T> StreamingResponseBody export(List<T> data, ExportFileDto exportFileDto) throws IOException;

  /**
   * Initialize export (e.g., header, structure) and return initial bytes.
   *
   * @param exportFileDto metadata for export
   * @return header/initial bytes
   * @throws IOException if export fails
   */
  byte[] init(ExportFileDto exportFileDto) throws IOException;

  /**
   * Write a batch of data and return its bytes.
   *
   * @param batchData     batch list to export
   * @param exportFileDto metadata for export
   * @param <T>           data type
   * @return byte array of the batch
   * @throws IOException if export fails
   */
  <T> byte[] writeBatch(List<T> batchData, ExportFileDto exportFileDto) throws IOException;

  /**
   * Finalize export (e.g., footer) and return closing bytes.
   *
   * @return closing bytes
   * @throws IOException if export fails
   */
  byte[] close() throws IOException;
}
