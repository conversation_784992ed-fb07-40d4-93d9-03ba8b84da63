[Modify]


[Add]
# -------------------------------#
# --------KANBA<PERSON> CORE------------#
# -------------------------------#
+ kanban.kafka.consumer.concurrency=3
+ kanban.kafka.consumer.execute-logic-timeout-seconds=600

+ kanban.authentication.keycloak.centrailized.url=http://10.1.27.43:8831/auth/realms/ms-core/protocol/openid-connect/token
+ kanban.authentication.keycloak.centrailized.username=mbmonitor_user
+ kanban.authentication.keycloak.centrailized.password=fb7bfb60-f5e3-4f8d-b4ad-3f22249b1fa0
+ kanban.authentication.keycloak.centrailized.public-key=-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3sROnOw4rj3ainkFaVHf05ha3yt4njOxJzljdebkJJckOXj1wag47nGSukVcsytXuhq8QrM6z8KpcSsAXNYNBgM7i7/X+ZxTf/b5cDKDpwiOzcWbMazii5/8ljIII9Z2sBOXQAf3Ev2NIgUlXBOuwhkQ9gFWmni+GvLnQxNVdJ/16ABelL1LCAbnDlX0VrhLnMQUihjf0Npy+jv5vm20DMzpk5rTo01XTTirrUmiaE76qILbwILqZDVwd4RlnGWD37N9qbjYTZ6AQ3s/yCaEgeJ0z4kzqP5G2oXvll9joV5P/Jub7WGRh9alj+VfhYp9MusTzlLJmqb8uvY3w7E67QIDAQAB-----END PUBLIC KEY-----
+ kanban.authentication.keycloak.centrailized.enable=false

# -------------------------------#
# --------MONITOR APP------------#
# -------------------------------#
+ mbmonitor.file-upload.dir=/data/mbmonitor/file-upload
+ mbmonitor.file-upload.expired-days=7
+ mbmonitor.teams.proxy.ip=**********
+ mbmonitor.teams.proxy.port=8080
+ monitor.external.execution.url=https://mbmonitor.tanzu-uat.mbbank.com.vn/api/external-execution/

# -------------------------------#
# --------SPRING APP------------#
# -------------------------------#
+ spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
+ monitor.rts.url=http://localhost:9000/api/rts

+ mbmonitor.monitor.web.executable.path.chromium=/data/mbmonitor/ms-playwright/chromium-1178/chrome-linux/chrome
+ mbmonitor.monitor.web.executable.path.firefox=/data/mbmonitor/ms-playwright/firefox-1487/firefox/firefox
+ mbmonitor.monitor.web.executable.path.driver=/data/mbmonitor/ms-playwright/driver
[Delete]
