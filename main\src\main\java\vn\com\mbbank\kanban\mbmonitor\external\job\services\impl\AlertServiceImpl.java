package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils.FORMAT_YYYY_MM_DD_T_HH_MM_SS;
import static vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils.convertDefaultStringDateToFormatedString;
import static vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils.formatDuration;

import java.io.IOException;
import java.text.MessageFormat;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertCursor;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileAlertRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.AlertResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NoteEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertDurationCompareOperator;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertDurationCompareUnit;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.NoteResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.S3FileService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.FileExporter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.factory.FileExporterFactory;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.AlertRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FileStorageService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.NoteService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ServiceService;


/**
 * Service Logic Alert service.
 */
@Service
@RequiredArgsConstructor
public class AlertServiceImpl extends BaseServiceImpl<AlertEntity, Long>
    implements AlertService {

  private final AlertRepository alertRepository;
  private final ServiceService serviceService;
  private final ApplicationService applicationService;
  private final AlertPriorityConfigService alertPriorityConfigService;
  private final NoteService noteService;
  private final FileStorageService fileStorageService;
  private final S3FileService s3FileService;

  private final NoteResponseMapper noteResponseMapper = NoteResponseMapper.INSTANCE;

  protected JpaCommonRepository<AlertEntity, Long> getRepository() {
    return alertRepository;
  }

  @Override
  public List<AlertEntity> findTopAlertsByAlertGroupIdAndStatus(Long alertGroupId,
                                                                AlertStatusEnum status,
                                                                int numberOfResult) {
    return alertRepository.findTopAlertsByAlertGroupIdAndStatus(alertGroupId, status,
        numberOfResult);
  }

  @Override
  public List<AlertEntity> findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(
      List<String> serviceIds,
      List<String> applicationIds,
      int alertGroupHandleTriggerInterval,
      AlertGroupStatusEnum status) {
    return alertRepository.findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(
        serviceIds,
        applicationIds,
        alertGroupHandleTriggerInterval, status);
  }

  @Override
  @Transactional
  public FileStorageEntity exportFile(ExportFileAlertRequest request, String userName, String filePath)
      throws IOException, BusinessException {

    FileExporter exporter = FileExporterFactory.getFileExporter(request.getExportDataModel().getExtension());
    int batchSize = CommonConstants.MAX_SIZE_BATCH_RECORD_EXPORT;
    int fileSize = request.getNumberOfResults();

    ExportFileDto fileDto = ExportFileDto.builder()
        .attributes(request.getAttributes())
        .title(genTitleForFileExport(request.getPaginationRequest()))
        .build();

    String contentType = request.getExportDataModel().getExtension().getContentType();
    String uploadId = s3FileService.beginMultipartUpload(filePath, contentType);

    try {
      AlertCursor cursor = new AlertCursor();
      int currentSize = 0;
      int partNumber = 1;
      boolean hasMoreData = true;
      List<String> etags = new ArrayList<>();

      boolean isFirstPart = true;

      while (hasMoreData && currentSize < fileSize) {
        AlertPaginationRequest paginationRequest = request.getPaginationRequest();
        paginationRequest.setPageSize(Math.min(batchSize, fileSize - currentSize));
        paginationRequest.setCursorAlertId(cursor.getCursorAlertId());
        paginationRequest.setCursorAlertCreatedDate(cursor.getCursorAlertCreatedDate());

        CursorPageResponse<AlertResponse, AlertCursor> pageResponse = findAll(paginationRequest);
        List<AlertResponse> alerts = pageResponse.getData();

        if (!alerts.isEmpty()) {
          alerts.forEach(alert -> {
            alert.setCreatedDate(convertDefaultStringDateToFormatedString(
                alert.getCreatedDate(), FORMAT_YYYY_MM_DD_T_HH_MM_SS));
            alert.setClosedDate(convertDefaultStringDateToFormatedString(
                alert.getClosedDate(), FORMAT_YYYY_MM_DD_T_HH_MM_SS));
          });

          if (isFirstPart) {
            byte[] headerBytes = exporter.init(fileDto);
            if (headerBytes.length > 0) {
              String headerEtag = s3FileService.uploadPart(filePath, uploadId, partNumber++, headerBytes);
              etags.add(headerEtag);
            }
            isFirstPart = false;
          }

          byte[] batchBytes = exporter.writeBatch(alerts, fileDto);
          if (batchBytes.length > 0) {
            String etag = s3FileService.uploadPart(filePath, uploadId, partNumber++, batchBytes);
            etags.add(etag);
          }

          currentSize += alerts.size();
          cursor = pageResponse.getNextCursor();
        } else {
          hasMoreData = false;
        }
      }

      // Export phần kết thúc (footer) của file
      byte[] footerBytes = exporter.close();
      if (footerBytes.length > 0) {
        String etag = s3FileService.uploadPart(filePath, uploadId, partNumber++, footerBytes);
        etags.add(etag);
      }

      s3FileService.completeMultipartUpload(filePath, uploadId, etags);
      return fileStorageService.registerFile(filePath, CommonConstants.MODULE_EXPORT, userName);
    } catch (Exception e) {
      s3FileService.abortMultipartUpload(filePath, uploadId);
      throw e;
    }
  }



  @Override
  public List<AlertEntity> findAlertKeyByGroupIdIn(List<Long> groupIds) {
    if (!groupIds.isEmpty()) {
      return alertRepository.findAlertKeyByGroupIdIn(groupIds);
    }
    return new ArrayList<>();
  }


  @Transactional(readOnly = true)
  public CursorPageResponse<AlertResponse, AlertCursor> findAll(AlertPaginationRequest paginationRequest) {
    var result = alertRepository.findAll(paginationRequest);
    if (CollectionUtils.isEmpty(result.getData())) {
      return result;
    }
    var groupIdSet = result.getData().stream().map(AlertResponse::getAlertGroupId).collect(Collectors.toSet());
    var noteMap =
        noteService.findAllByAlertGroupIdInOrderByCreatedDateDesc(groupIdSet.stream().toList()).stream().collect(
            Collectors.groupingBy(NoteEntity::getAlertGroupId));
    for (AlertResponse alert : result.getData()) {
      var notes = noteMap.get(alert.getAlertGroupId());
      if (CollectionUtils.isNotEmpty(notes)) {
        alert.setNotes(noteResponseMapper.map(notes));
      }
      // format duration
      try {
        var duration = Long.parseLong(alert.getClosedDuration());
        alert.setClosedDuration(formatDuration(duration, ChronoUnit.SECONDS));
      } catch (NumberFormatException e) {
        alert.setClosedDuration("");
      }
    }
    return result;
  }

  protected List<String> genTitleForFileExport(AlertPaginationRequest request) {
    List<String> conditions = new ArrayList<>();
    conditions.add("Alert - Filter by condition: ");
    addCondition(conditions, "Range date", request.getRangeDate());
    addCondition(conditions, "Service Name",
        serviceService.findAllNameByIdIn(request.getServiceIds()));
    addCondition(conditions, "Application Name",
        applicationService.findAllNameByIdIn(request.getApplicationIds()));
    addCondition(conditions, "Priorities",
        alertPriorityConfigService.findAllNameByIdIn(request.getAlertPriorityConfigIds()));
    addCondition(conditions, "Status", request.getStatuses());
    addCondition(conditions, "Alert Content", request.getContent());
    addCondition(conditions, "Group ID", request.getAlertGroupId());
    addCondition(conditions, "Contact", request.getRecipient());
    addCondition(conditions, "Closed By", request.getClosedBy());
    addDurationCondition(conditions, request.getCloseDurationOperator(), request.getClosedDuration(),
        request.getCloseDurationUnit());

    return conditions;
  }

  protected void addDurationCondition(List<String> conditions, AlertDurationCompareOperator operator,
                                      Long closedDuration, AlertDurationCompareUnit unit) {
    if (Objects.nonNull(operator) && Objects.nonNull(closedDuration) && Objects.nonNull(unit)) {
      conditions.add(
          MessageFormat.format("Closed Duration {0} {1} {2}", operator.getOperator(), closedDuration, unit.getLabel()));
    }
  }

  protected static void addCondition(List<String> conditions, String label, Object value) {
    Optional.ofNullable(value)
        .filter(v -> {
          if (v instanceof List) {
            return !((List<?>) v).isEmpty();
          } else {
            return !v.toString().isEmpty();
          }
        })
        .ifPresent(v -> conditions.add(label + " = " + v));
  }
}
