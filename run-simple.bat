@echo off
setlocal enabledelayedexpansion

echo Starting MBMonitor External Job (Simple Mode)...

set MAIN_JAR=main\target\external-job-1.0-SNAPSHOT.jar
set LIB_DIR=main\target\lib
set M2_REPO=%USERPROFILE%\.m2\repository

if not exist "%MAIN_JAR%" (
    echo Error: JAR file not found: %MAIN_JAR%
    echo Please run: mvn clean package -DskipTests
    pause
    exit /b 1
)

echo Creating lib directory and copying essential dependencies...
if not exist "%LIB_DIR%" mkdir "%LIB_DIR%"

REM Copy mbmonitor-common
if exist "%M2_REPO%\vn\com\mbbank\kanban\mbmonitor\common\mbmonitor-common\1.0-SNAPSHOT\mbmonitor-common-1.0-SNAPSHOT.jar" (
    copy "%M2_REPO%\vn\com\mbbank\kanban\mbmonitor\common\mbmonitor-common\1.0-SNAPSHOT\mbmonitor-common-1.0-SNAPSHOT.jar" "%LIB_DIR%\" >nul
)

REM Copy Spring Boot core dependencies
if exist "%M2_REPO%\org\springframework\boot\spring-boot-starter\3.3.10\spring-boot-starter-3.3.10.jar" (
    copy "%M2_REPO%\org\springframework\boot\spring-boot-starter\3.3.10\spring-boot-starter-3.3.10.jar" "%LIB_DIR%\" >nul
)

echo Building classpath...
set CLASSPATH=%MAIN_JAR%
for %%i in (%LIB_DIR%\*.jar) do (
    set CLASSPATH=!CLASSPATH!;%%i
)

echo Running application...
echo Classpath: %CLASSPATH%
echo.

java -cp "%CLASSPATH%" vn.com.mbbank.kanban.mbmonitor.external.job.Main %*

if %ERRORLEVEL% neq 0 (
    echo.
    echo Application failed. Error code: %ERRORLEVEL%
    echo.
    echo Try running with Maven instead:
    echo mvn exec:java -Dexec.mainClass="vn.com.mbbank.kanban.mbmonitor.external.job.Main" -pl main
    pause
)
