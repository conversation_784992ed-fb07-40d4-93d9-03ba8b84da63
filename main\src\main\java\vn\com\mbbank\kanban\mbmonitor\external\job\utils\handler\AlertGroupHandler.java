package vn.com.mbbank.kanban.mbmonitor.external.job.utils.handler;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertPriorityConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigConditionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupOutputEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.CustomObjectUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleCondition;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleElement;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertGroupConfigConditionService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertGroupConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertGroupConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertGroupService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AutoTriggerActionService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.CustomObjectService;

/**
 * AlertGroupHandler.
 */
@Component
@RequiredArgsConstructor
public class AlertGroupHandler {

  private static final Logger logger = LoggerFactory.getLogger(AlertGroupHandler.class);

  private final AlertGroupConfigService alertGroupConfigService;
  private final AlertGroupConfigConditionService alertGroupConfigConditionService;
  private final AlertGroupService alertGroupService;
  private final CustomObjectService customObjectService;
  private final AlertService alertService;
  private final AlertPriorityConfigService alertPriorityConfigService;
  private final AlertGroupConfigDependencyService alertGroupConfigDependencyService;
  private final ApplicationService applicationService;
  private final AutoTriggerActionService autoTriggerActionService;

  @Value("${mbmonitor.alertGroup.alertGroupHandleTriggerInterval}")
  private int createdTimeSecondAgo;

  @Value("${mbmonitor.alertGroup.amountOfAlertProcessAtTheSameTime}")
  private int processAlertAmounts;

  /**
   * handle group alert.
   */
  public void group() {
    var alerts = alertService.findTopAlertsByAlertGroupIdAndStatus(0L, AlertStatusEnum.NEW, processAlertAmounts);
    var alertGroupConfigs = alertGroupConfigService.findAllByDeletedAndActive(false, true);
    alertGroupConfigs.sort(Comparator.comparingInt(AlertGroupConfigEntity::getPosition));
    List<AlertEntity> handleAlerts = new ArrayList<>(alerts);
    for (AlertGroupConfigEntity alertGroupConfig : alertGroupConfigs) {
      handleAlerts = handleAlertGroupConfig(alertGroupConfig, handleAlerts);
    }
    List<AlertEntity> singleAlerts = alerts.stream().filter(alert
            -> Objects.equals(alert.getAlertGroupId(), 0L)).toList();
    if (!CollectionUtils.isEmpty(singleAlerts)) {
      createSingleAlertGroups(singleAlerts);
    }
    try {
      var groupIds = alerts.stream()
              .map(AlertEntity::getAlertGroupId)
              .filter(id -> !Objects.equals(id, 0L))
              .distinct()
              .toList();
      autoTriggerActionService.triggerJob(singleAlerts, groupIds);
    } catch (Exception e) {
      logger.error("Error when call trigger job : {}", e.getMessage());
    }
  }

  // Return a list alerts unresolved
  protected List<AlertEntity> handleAlertGroupConfig(AlertGroupConfigEntity alertGroupConfig,
                                                     List<AlertEntity> alerts) {
    var alertGroupConfigDependencies =
        alertGroupConfigDependencyService.findAllByAlertGroupConfigId(alertGroupConfig.getId());
    var serviceIds = new ArrayList<String>();
    var applicationIds = new ArrayList<String>();
    var serviceIdsWithAllApplication = new ArrayList<String>();
    for (AlertGroupConfigDependencyEntity dependency : alertGroupConfigDependencies) {
      if (AlertGroupConfigDependencyTypeEnum.APPLICATION.equals(dependency.getType())) {
        applicationIds.add(dependency.getDependencyId());
      } else if (AlertGroupConfigDependencyTypeEnum.SERVICE.equals(dependency.getType())) {
        serviceIds.add(dependency.getDependencyId());
      } else {
        serviceIds.add(dependency.getDependencyId());
        serviceIdsWithAllApplication.add(dependency.getDependencyId());
      }
    }
    var additionalApplicationIds = applicationService
        .findAllByServiceIdInAndDeleted(serviceIdsWithAllApplication, false)
        .stream().map(ApplicationEntity::getId).toList();

    var isMultipleCondition = AlertGroupConfigTypeEnum.MULTIPLE_CONDITION.equals(alertGroupConfig.getType());
    applicationIds.addAll(additionalApplicationIds);
    var handleAlerts = new ArrayList<>(
        alertService.findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(serviceIds, applicationIds,
            createdTimeSecondAgo, isMultipleCondition ? null : AlertGroupStatusEnum.NEW));
    for (AlertEntity alert : alerts) {
      if (serviceIds.contains(alert.getServiceId()) && applicationIds.contains(alert.getApplicationId())) {
        handleAlerts.add(alert);
      }
    }
    if (CollectionUtils.isEmpty(handleAlerts)) {
      return alerts;
    }
    var groupedAlerts = isMultipleCondition ? handleMultipleConditionGroup(alertGroupConfig, handleAlerts) :
        handleSameObjectValueGroup(alertGroupConfig, handleAlerts);
    var singleAlerts = new ArrayList<>(alerts);
    singleAlerts.removeAll(groupedAlerts);
    return singleAlerts;
  }

  // Return a list of alert grouped
  protected List<AlertEntity> handleSameObjectValueGroup(AlertGroupConfigEntity alertGroupConfig,
                                                         List<AlertEntity> alerts) {
    var groupedAlerts = new ArrayList<AlertEntity>();
    var alertGroupConfigId = alertGroupConfig.getId();
    var customObjects = customObjectService.findAllByAlertGroupConfigId(alertGroupConfigId);
    var groupValueMap =
        alerts.stream().collect(Collectors.groupingBy(alert -> getGroupValue(customObjects, alert.getContent())));
    var alertGroups = alertGroupService.findByAlertGroupConfigIdAndMatchValueInAndStatus(alertGroupConfigId,
        groupValueMap.keySet().stream().toList(), AlertGroupStatusEnum.NEW);
    var alertGroupMap =
        alertGroups.stream().collect(Collectors.toMap(AlertGroupEntity::getMatchValue, Function.identity()));
    var emptyValue = customObjects.stream().map(customObject -> StringUtils.EMPTY)
        .collect(Collectors.joining(CommonConstants.DEFAULT_DELIMITER));
    for (String groupValue : groupValueMap.keySet()) {
      // Handler with alerts can be group together
      if (StringUtils.isNotBlank(groupValue) && !emptyValue.equals(groupValue)) {
        var groupableAlerts = groupValueMap.get(groupValue);
        // Add groupableAlert to a matched group
        if (alertGroupMap.containsKey(groupValue)) {
          groupedAlerts.addAll(
              addAlertsToExistedGroup(groupableAlerts, alertGroupConfig, alertGroupMap.get(groupValue)));
          continue;
        }
        // create a new group
        if (groupableAlerts.size() >= 2) {
          groupedAlerts.addAll(addAlertsToNewGroup(groupableAlerts, alertGroupConfig, groupValue));
        }
      }
    }
    return groupedAlerts;
  }

  // Return a list of alert grouped
  protected List<AlertEntity> handleMultipleConditionGroup(AlertGroupConfigEntity alertGroupConfig,
                                                           List<AlertEntity> alerts) {
    var alertGroupConfigId = alertGroupConfig.getId();
    var alertGroupOptional =
        alertGroupService.findByAlertGroupConfigIdAndStatus(alertGroupConfigId, AlertGroupStatusEnum.NEW);
    var customObjectMap = customObjectService.findAll().stream()
        .collect(Collectors.toMap(CustomObjectEntity::getId, Function.identity()));
    var alertGroupConfigConditions = alertGroupConfigConditionService.findAllByAlertGroupConfigId(alertGroupConfigId);
    var alertGroupConfigConditionMap = alertGroupConfigConditions.stream().collect(
        Collectors.toMap(AlertGroupConfigConditionEntity::getId,
            condition -> getCustomObjectIds(condition.getRuleGroup()).stream().map(customObjectMap::get).toList()));
    if (alertGroupOptional.isPresent()) {
      var groupableAlerts = alerts.stream().filter(alert -> alertGroupConfigConditions.stream().anyMatch(
          condition -> condition.getRuleGroup()
              .check(CustomObjectUtils
                  .getAlertConditionValueMap(alert, alertGroupConfigConditionMap.get(condition.getId()))))).toList();
      if (!CollectionUtils.isEmpty(groupableAlerts)) {
        return addAlertsToExistedGroup(groupableAlerts, alertGroupConfig, alertGroupOptional.get());
      }
    } else {
      var alertsMatchedConditionMap = alertGroupConfigConditions.stream().collect(Collectors.toMap(Function.identity(),
          (condition) -> alerts.stream().filter(alert -> condition.getRuleGroup()
              .check(CustomObjectUtils
                  .getAlertConditionValueMap(alert, alertGroupConfigConditionMap.get(condition.getId())))).toList()));
      // alert group condition have at least 1 alert matched.
      if (alertsMatchedConditionMap.values().stream().allMatch(x -> !CollectionUtils.isEmpty(x))) {
        var groupableAlerts =
            new HashSet<>(alertsMatchedConditionMap.values().stream().flatMap(Collection::stream).toList()).stream()
                .toList();
        if (groupableAlerts.size() >= alertsMatchedConditionMap.size()) {
          return addAlertsToNewGroup(groupableAlerts, alertGroupConfig, null);
        }
      }
    }
    return List.of();
  }

  protected String getGroupValue(List<CustomObjectEntity> customObjects, String alertContent) {
    return customObjects.stream().sorted(Comparator.comparingLong(CustomObjectEntity::getId))
        .map(customObject -> CustomObjectUtils.evaluate(alertContent, customObject))
        .collect(Collectors.joining(CommonConstants.DEFAULT_DELIMITER));
  }

  protected List<Long> getCustomObjectIds(RuleGroupType ruleGroup) {
    var customObjectIds = new ArrayList<Long>();
    var rules = ruleGroup.getRules();
    for (RuleElement rule : rules) {
      if (rule instanceof RuleGroupType ruleGroupType) {
        customObjectIds.addAll(getCustomObjectIds(ruleGroupType));
      } else if (rule instanceof RuleCondition<?> ruleCondition) {
        var fieldName = ruleCondition.getField();
        try {
          Long customObjectId = Long.valueOf(fieldName);
          customObjectIds.add(customObjectId);
        } catch (NumberFormatException exception) {
          // ignore
        }
      }
    }
    return customObjectIds;
  }

  protected List<AlertEntity> addAlertsToExistedGroup(List<AlertEntity> groupableAlerts,
                                                      AlertGroupConfigEntity alertGroupConfig,
                                                      AlertGroupEntity alertGroup) {
    alertGroupService.deleteByIdIn(
        groupableAlerts.stream().map(AlertEntity::getAlertGroupId).filter(Objects::nonNull).toList());
    alertService.saveAll(groupableAlerts.stream().peek(alert -> alert.setAlertGroupId(alertGroup.getId())).toList());
    var primaryAlert = findNewPrimaryAlert(alertGroupConfig, groupableAlerts, alertGroup.getPrimaryAlertId());
    if (Objects.nonNull(primaryAlert)) {
      alertGroup.setPrimaryAlertId(primaryAlert.getId());
    }
    alertGroupService.save(alertGroup);
    return groupableAlerts;
  }

  protected List<AlertEntity> addAlertsToNewGroup(List<AlertEntity> groupableAlerts,
                                                  AlertGroupConfigEntity alertGroupConfig, String matchValue) {
    // Create new group when have at least 2 alerts
    if (groupableAlerts.size() >= 2) {
      var newAlertGroup = new AlertGroupEntity();

      newAlertGroup.setStatus(AlertGroupStatusEnum.NEW);
      newAlertGroup.setMatchValue(matchValue);
      newAlertGroup.setAlertGroupConfigId(alertGroupConfig.getId());
      var primaryAlert = findNewPrimaryAlert(alertGroupConfig, groupableAlerts, null);
      newAlertGroup.setPrimaryAlertId(
          Objects.isNull(primaryAlert) ? groupableAlerts.get(0).getId() : primaryAlert.getId());
      newAlertGroup.setServiceId(primaryAlert.getServiceId());
      newAlertGroup.setApplicationId(primaryAlert.getApplicationId());
      alertGroupService.save(newAlertGroup);
      var groupedAlerts = new ArrayList<>(groupableAlerts);
      if (AlertGroupOutputEnum.CUSTOM.equals(alertGroupConfig.getAlertOutput())) {
        groupedAlerts.add(primaryAlert);
      }
      alertGroupService.deleteByIdIn(
          groupableAlerts.stream().map(AlertEntity::getAlertGroupId).filter(Objects::nonNull).toList());
      alertService.saveAll(
          groupedAlerts.stream().peek(alert -> alert.setAlertGroupId(newAlertGroup.getId())).toList());
      return groupableAlerts;
    }
    return List.of();
  }

  protected void createSingleAlertGroups(List<AlertEntity> alerts) {
    // Create a single group alert
    var singleGroupAlerts = alerts.stream().map(alert -> {
      var singleAlertGroup = new AlertGroupEntity();
      singleAlertGroup.setServiceId(alert.getServiceId());
      singleAlertGroup.setApplicationId(alert.getApplicationId());
      singleAlertGroup.setStatus(AlertGroupStatusEnum.NEW);
      singleAlertGroup.setPrimaryAlertId(alert.getId());
      singleAlertGroup.setAlertGroupConfigId(0L);
      return singleAlertGroup;
    }).toList();
    if (!CollectionUtils.isEmpty(singleGroupAlerts)) {
      alertGroupService.saveAll(singleGroupAlerts);
      var alertGroupMap = singleGroupAlerts.stream()
          .collect(Collectors.toMap(AlertGroupEntity::getPrimaryAlertId, Function.identity()));
      alertService.saveAll(
          alerts.stream().peek(alert -> alert.setAlertGroupId(alertGroupMap.get(alert.getId()).getId())).toList());
    }
  }

  protected AlertEntity findNewPrimaryAlert(AlertGroupConfigEntity alertGroupConfig, List<AlertEntity> alerts,
                                            Long oldPrimaryAlertId) {
    var alertOutputType = alertGroupConfig.getAlertOutput();
    var handleAlerts = new ArrayList<>(alerts);
    AlertEntity oldPrimaryAlert = null;
    if (Objects.nonNull(oldPrimaryAlertId)) {
      oldPrimaryAlert = alertService.findById(oldPrimaryAlertId);
      if (Objects.nonNull(oldPrimaryAlert)) {
        handleAlerts.add(oldPrimaryAlert);
      }
    }
    if (CollectionUtils.isEmpty(handleAlerts)) {
      return null;
    }
    return switch (alertOutputType) {
      case LASTED_ALERT -> handleAlerts.stream()
          .max(Comparator.comparing(AlertEntity::getCreatedDate).thenComparing(AlertEntity::getId)).get();
      case HIGHEST_PRIORITY -> getHighestPriorityAlert(handleAlerts);
      case LOWEST_PRIORITY -> getLowestPriorityAlert(handleAlerts);
      case CUSTOM -> getCustomPrimaryAlert(alertGroupConfig, oldPrimaryAlert);
    };
  }

  private AlertEntity getHighestPriorityAlert(List<AlertEntity> alerts) {
    var alertPriorityConfigMap = alertPriorityConfigService.findAll().stream()
        .collect(Collectors.toMap(AlertPriorityConfigEntity::getId, Function.identity()));
    return alerts.stream().reduce(alerts.get(0), (highestPriorityAlert, currentAlert) -> {
      var currentPriorityConfigId = currentAlert.getAlertPriorityConfigId();
      var highestPriorityConfigId = highestPriorityAlert.getAlertPriorityConfigId();
      if (AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID.equals(currentPriorityConfigId)) {
        return highestPriorityAlert;
      }
      if (AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID.equals(highestPriorityConfigId)) {
        return currentAlert;
      }
      var currentPriorityConfig = alertPriorityConfigMap.get(currentPriorityConfigId);
      var highestPriorityConfig = alertPriorityConfigMap.get(highestPriorityConfigId);
      if (Objects.isNull(currentPriorityConfig) || Objects.isNull(highestPriorityConfig)) {
        return highestPriorityAlert;
      }
      return NumberUtils.compare(highestPriorityConfig.getPosition(), currentPriorityConfig.getPosition()) < 0
          ? highestPriorityAlert : currentAlert;
    });
  }

  private AlertEntity getLowestPriorityAlert(List<AlertEntity> alerts) {
    var alertPriorityConfigMap = alertPriorityConfigService.findAll().stream()
        .collect(Collectors.toMap(AlertPriorityConfigEntity::getId, Function.identity()));
    return alerts.stream().reduce(alerts.get(0), (lowestPriorityAlert, currentAlert) -> {
      var currentPriorityConfigId = currentAlert.getAlertPriorityConfigId();
      var lowestPriorityConfigId = lowestPriorityAlert.getAlertPriorityConfigId();
      if (AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID.equals(currentPriorityConfigId)) {
        return currentAlert;
      }
      if (AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID.equals(lowestPriorityConfigId)) {
        return lowestPriorityAlert;
      }
      var currentPriorityConfig = alertPriorityConfigMap.get(currentPriorityConfigId);
      var lowestPriorityConfig = alertPriorityConfigMap.get(lowestPriorityConfigId);
      if (Objects.isNull(currentPriorityConfig) || Objects.isNull(lowestPriorityConfig)) {
        return lowestPriorityAlert;
      }
      return NumberUtils.compare(lowestPriorityConfig.getPosition(), currentPriorityConfig.getPosition()) > 0
          ? lowestPriorityAlert : currentAlert;
    });
  }

  private AlertEntity getCustomPrimaryAlert(AlertGroupConfigEntity alertGroupConfig, AlertEntity oldPrimaryAlert) {
    if (Objects.nonNull(oldPrimaryAlert)) {
      return oldPrimaryAlert;
    }
    var newPrimaryAlert = new AlertEntity();
    newPrimaryAlert.setAlertPriorityConfigId(alertGroupConfig.getCustomPriorityConfigId());
    newPrimaryAlert.setServiceId(alertGroupConfig.getCustomServiceId());
    newPrimaryAlert.setApplicationId(alertGroupConfig.getCustomApplicationId());
    newPrimaryAlert.setStatus(AlertStatusEnum.NEW);
    newPrimaryAlert.setContent(alertGroupConfig.getCustomContent());
    newPrimaryAlert.setRecipient(alertGroupConfig.getCustomRecipient());
    return alertService.save(newPrimaryAlert);
  }
}
