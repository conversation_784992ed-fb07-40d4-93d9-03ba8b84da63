package vn.com.mbbank.kanban.mbmonitor.common.utils.export;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AttributeInfoDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;

public class XlsxExporter implements FileExporter {

  private SXSSFWorkbook workbook;
  private Sheet sheet;
  private int rowIndex;
  private List<AttributeInfoDto> sortedAttributes;

  @Override
  public <T> StreamingResponseBody export(List<T> data, ExportFileDto exportFileDto) throws IOException {
    return outputStream -> {
      init(exportFileDto);
      writeBatch(data, exportFileDto);
      flushWorkbook(outputStream);
      workbook.dispose();
      workbook.close();
    };
  }

  @Override
  public byte[] init(ExportFileDto exportFileDto) throws IOException {
    workbook = new SXSSFWorkbook(100);
    sheet = workbook.createSheet();
    rowIndex = 0;

    sortedAttributes = exportFileDto.getAttributes().stream()
        .sorted(Comparator.comparingLong(AttributeInfoDto::getPosition))
        .collect(Collectors.toList());
    setColumnWidth(sortedAttributes, sheet);

    if (exportFileDto.getTitle() != null && !exportFileDto.getTitle().isEmpty()) {
      writeTitleExportFile(rowIndex, sheet, exportFileDto.getTitle(), workbook);
      rowIndex += exportFileDto.getTitle().size() + 1;
    }

    Row headerRow = sheet.createRow(rowIndex);
    writeHeaderExportFile(headerRow, sortedAttributes, workbook);
    rowIndex++;

    return flushWorkbookToByteArray();
  }

  @Override
  public <T> byte[] writeBatch(List<T> batchData, ExportFileDto exportFileDto) throws IOException {
    var transformedData = ExcelUtils.transformDataExport(batchData, sortedAttributes);
    writeDataExportFile(sheet, rowIndex, transformedData);
    rowIndex += transformedData.size();
    return flushWorkbookToByteArray();
  }

  @Override
  public byte[] close() throws IOException {
    byte[] finalBytes = flushWorkbookToByteArray();
    workbook.dispose();
    workbook.close();
    return finalBytes;
  }

  private void flushWorkbook(OutputStream outputStream) throws IOException {
    workbook.write(outputStream);
    outputStream.flush();
  }

  private byte[] flushWorkbookToByteArray() throws IOException {
    try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
      workbook.write(baos);
      baos.flush();
      return baos.toByteArray();
    }
  }

  // --- Helper methods ---

  public static void writeTitleExportFile(int rowNum, Sheet sheet, List<String> titles, Workbook workbook) {
    CellStyle cellStyle = workbook.createCellStyle();
    Font font = workbook.createFont();
    font.setBold(true);
    font.setItalic(true);
    font.setColor(IndexedColors.BLUE.getIndex());
    cellStyle.setFont(font);

    for (String title : titles) {
      Row titleRow = sheet.createRow(rowNum++);
      Cell cell = titleRow.createCell(0);
      cell.setCellStyle(cellStyle);
      cell.setCellValue(title);
    }
  }

  public static void writeHeaderExportFile(Row headerRow,
                                           List<AttributeInfoDto> attributeInfoList,
                                           Workbook workbook) {
    CellStyle headerStyle = workbook.createCellStyle();
    headerStyle.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
    headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

    Font headerFont = workbook.createFont();
    headerFont.setBold(true);
    headerStyle.setFont(headerFont);

    int cellNum = 0;
    for (AttributeInfoDto attribute : attributeInfoList) {
      Cell cell = headerRow.createCell(cellNum++);
      cell.setCellValue(attribute.getAttributeName());
      cell.setCellStyle(headerStyle);
    }
  }

  public static void writeDataExportFile(Sheet sheet, int rowNum,
                                         List<List<AttributeInfoDto>> transformedData) {
    for (List<AttributeInfoDto> row : transformedData) {
      Row excelRow = sheet.createRow(rowNum++);
      int cellNum = 0;
      for (AttributeInfoDto attribute : row) {
        Cell cell = excelRow.createCell(cellNum++);
        cell.setCellValue(attribute.getValue());
      }
    }
  }

  public static void setColumnWidth(List<AttributeInfoDto> attributes, Sheet sheet) {
    for (int i = 0; i < attributes.size(); i++) {
      if (attributes.get(i).getWidth() != null) {
        sheet.setColumnWidth(i, attributes.get(i).getWidth());
      }
    }
  }
}
