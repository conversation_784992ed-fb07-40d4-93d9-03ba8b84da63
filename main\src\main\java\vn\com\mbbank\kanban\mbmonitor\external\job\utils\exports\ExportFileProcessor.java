package vn.com.mbbank.kanban.mbmonitor.external.job.utils.exports;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.services.S3FileService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.FileExporter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.factory.FileExporterFactory;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FileStorageService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/04/2025
 */
@Component
@RequiredArgsConstructor
public class ExportFileProcessor {

  private final FileStorageService fileStorageService;
  private final S3FileService s3FileService;
  /**
   * Exports data to a file in batches and registers the file information.
   *
   * @param request       the export request details
   * @param userName      the username performing the export
   * @param filePath      the path to save the file
   * @param fileDto       file metadata for export
   * @param batchFetcher  function to fetch data in batches
   * @param <T>           type of data to export
   * @return              registered file information
   * @throws IOException         if file writing fails
   * @throws BusinessException   if a business error occurs
   */
  public <T> FileStorageEntity exportFileCommon(
      ExportFileRequest request,
      String userName,
      String filePath,
      ExportFileDto fileDto,
      BatchFetcher<T> batchFetcher
  ) throws IOException, BusinessException {

    FileExporter exporter = FileExporterFactory.getFileExporter(request.getExportDataModel().getExtension());
    int fileSize = request.getNumberOfResults();
    int batchSize = CommonConstants.MAX_SIZE_BATCH_RECORD_EXPORT;
    int currentOffset = 0;

    var contentType = request.getExportDataModel().getExtension().getContentType();
    String uploadId = s3FileService.beginMultipartUpload(filePath, contentType);
    List<String> etags = new ArrayList<>();
    try {
      boolean hasMoreData = true;
      int partNumber = 1;
      boolean isFirstPart = true;

      // Upload header first
      byte[] headerBytes = exporter.init(fileDto);
      if (headerBytes.length > 0) {
        String headerETag = s3FileService.uploadPart(filePath, uploadId, partNumber++, headerBytes);
        etags.add(headerETag);
      }

      while (hasMoreData && currentOffset < fileSize) {
        int remaining = fileSize - currentOffset;
        int currentBatchSize = Math.min(batchSize, remaining);
        List<T> batch = batchFetcher.fetch(currentOffset, currentBatchSize);

        if (!KanbanCommonUtil.isEmpty(batch)) {
          byte[] batchBytes = exporter.writeBatch(batch, fileDto);
          if (batchBytes.length > 0) {
            String eTag = s3FileService.uploadPart(filePath, uploadId, partNumber++, batchBytes);
            etags.add(eTag);
          }
          currentOffset += batch.size();
        } else {
          hasMoreData = false;
        }
      }

      // Upload footer
      byte[] footerBytes = exporter.close();
      if (footerBytes.length > 0) {
        String footerETag = s3FileService.uploadPart(filePath, uploadId, partNumber++, footerBytes);
        etags.add(footerETag);
      }

      s3FileService.completeMultipartUpload(filePath, uploadId, etags);

    } catch (Exception ex) {
      s3FileService.abortMultipartUpload(filePath, uploadId);
      throw new IOException("Multipart upload failed: " + ex.getMessage(), ex);
    }

    return fileStorageService.registerFile(filePath, CommonConstants.MODULE_EXPORT, userName);
  }


}
