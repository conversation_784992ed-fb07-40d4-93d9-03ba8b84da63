package vn.com.mbbank.kanban.mbmonitor.external.job.utils.exports;

import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ExportFileRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.FileExporter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.factory.FileExporterFactory;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FileStorageService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/04/2025
 */
@Component
@RequiredArgsConstructor
public class ExportFileProcessor {

  private final FileStorageService fileStorageService;

  /**
   * Exports data to a file in batches and registers the file information.
   *
   * @param request       the export request details
   * @param userName      the username performing the export
   * @param filePath      the path to save the file
   * @param fileDto       file metadata for export
   * @param batchFetcher  function to fetch data in batches
   * @param <T>           type of data to export
   * @return              registered file information
   * @throws IOException         if file writing fails
   * @throws BusinessException   if a business error occurs
   */
  public <T> FileStorageEntity exportFileCommon(
      ExportFileRequest request,
      String userName,
      String filePath,
      ExportFileDto fileDto,
      BatchFetcher<T> batchFetcher
  ) throws IOException, BusinessException {

    FileExporter exporter = FileExporterFactory.getFileExporter(request.getExportDataModel().getExtension());
    int fileSize = request.getNumberOfResults();
    int batchSize = CommonConstants.MAX_SIZE_BATCH_RECORD_EXPORT;
    int currentOffset = 0;

    try (FileOutputStream fos = new FileOutputStream(filePath);
         BufferedOutputStream bos = new BufferedOutputStream(fos)) {

      exporter.init(fileDto, bos);
      boolean hasMoreData = true;

      while (hasMoreData && currentOffset < fileSize && currentOffset % batchSize == 0) {
        int remaining = fileSize - currentOffset;
        int currentBatchSize = Math.min(batchSize, remaining);

        List<T> batch = batchFetcher.fetch(currentOffset, currentBatchSize);
        if (!KanbanCommonUtil.isEmpty(batch)) {
          exporter.writeBatch(batch, fileDto, bos);
          currentOffset += batch.size();
        } else {
          hasMoreData = false;
        }
      }

      exporter.close(bos);
    }

    return fileStorageService.registerFile(filePath, CommonConstants.MODULE_EXPORT, userName);
  }

}
