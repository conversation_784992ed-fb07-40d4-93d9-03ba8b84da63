package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import jakarta.annotation.PostConstruct;

import java.io.IOException;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.*;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.*;
import software.amazon.awssdk.services.s3.model.*;
import vn.com.mbbank.kanban.mbmonitor.common.services.S3FileService;

/**
 * Implementation of S3FileService using AWS SDK v2.
 */
@Slf4j
@Service
public class S3FileServiceImpl implements S3FileService {

  @Value("${s3.access-key}")
  private String accessKey;

  @Value("${s3.secret-key}")
  private String secretKey;

  @Value("${s3.endpoint}")
  private String endpoint;

  @Value("${s3.bucket-name}")
  private String bucketName;

  private S3Client s3Client;


  @PostConstruct
  public void init() {
    s3Client = S3Client.builder()
      .credentialsProvider(StaticCredentialsProvider.create(
        AwsBasicCredentials.create(accessKey, secretKey)))
      .endpointOverride(URI.create(endpoint))
      .region(Region.of("ap-southeast-1"))
      .serviceConfiguration(S3Configuration.builder()
        .pathStyleAccessEnabled(true)
        .checksumValidationEnabled(false)
        .build())
      .build();
  }

  @Override
  public void upload(String key, byte[] content, String contentType) {
    s3Client.putObject(
      PutObjectRequest.builder()
        .bucket(bucketName)
        .key(key)
        .contentType(contentType)
        .build(),
      RequestBody.fromBytes(content)
    );
  }

  @Override
  public byte[] download(String key) {
    return s3Client.getObjectAsBytes(
      GetObjectRequest.builder()
        .bucket(bucketName)
        .key(key)
        .build()
    ).asByteArray();
  }

  @Override
  public void delete(String key) {
    s3Client.deleteObject(DeleteObjectRequest.builder()
      .bucket(bucketName)
      .key(key)
      .build());
  }

  @Override
  public boolean exists(String key) {
    try {
      s3Client.headObject(HeadObjectRequest.builder()
        .bucket(bucketName)
        .key(key)
        .build());
      return true;
    } catch (S3Exception e) {
      return false;
    }
  }

  @Override
  public S3Client getS3Client() {
    return s3Client;
  }

  @Override
  public String getBucketName() {
    return bucketName;
  }


  @Override
  public String beginMultipartUpload(String key, String contentType) {
    CreateMultipartUploadRequest request = CreateMultipartUploadRequest.builder()
        .bucket(bucketName)
        .key(key)
        .contentType(contentType)
        .build();

    CreateMultipartUploadResponse response = s3Client.createMultipartUpload(request);
    return response.uploadId();
  }

  @Override
  public void abortMultipartUpload(String key, String uploadId) {
    AbortMultipartUploadRequest abortRequest = AbortMultipartUploadRequest.builder()
        .bucket(bucketName)
        .key(key)
        .uploadId(uploadId)
        .build();

    s3Client.abortMultipartUpload(abortRequest);
  }

  @Override
  public void completeMultipartUpload(String key, String uploadId, List<String> etags) {
    List<CompletedPart> completedParts = new ArrayList<>();
    for (int i = 0; i < etags.size(); i++) {
      completedParts.add(
          CompletedPart.builder()
              .partNumber(i + 1) // partNumber bắt đầu từ 1
              .eTag(etags.get(i))
              .build()
      );
    }

    CompletedMultipartUpload completedMultipartUpload = CompletedMultipartUpload.builder()
        .parts(completedParts)
        .build();

    CompleteMultipartUploadRequest completeRequest = CompleteMultipartUploadRequest.builder()
        .bucket(bucketName)
        .key(key)
        .uploadId(uploadId)
        .multipartUpload(completedMultipartUpload)
        .build();

    s3Client.completeMultipartUpload(completeRequest);
  }

  @Override
  public String uploadPart(String key, String uploadId, int partNumber, byte[] partBytes) {
    UploadPartRequest uploadPartRequest = UploadPartRequest.builder()
        .bucket(bucketName)
        .key(key)
        .uploadId(uploadId)
        .partNumber(partNumber)
        .contentLength((long) partBytes.length)
        .build();

    UploadPartResponse response = s3Client.uploadPart(
        uploadPartRequest,
        RequestBody.fromBytes(partBytes)
    );

    return response.eTag();
  }


}
