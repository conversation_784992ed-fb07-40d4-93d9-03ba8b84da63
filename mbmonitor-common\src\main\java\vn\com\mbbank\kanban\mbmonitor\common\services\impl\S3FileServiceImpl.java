package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import jakarta.annotation.PostConstruct;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.model.AbortMultipartUploadRequest;
import software.amazon.awssdk.services.s3.model.CompleteMultipartUploadRequest;
import software.amazon.awssdk.services.s3.model.CompletedMultipartUpload;
import software.amazon.awssdk.services.s3.model.CompletedPart;
import software.amazon.awssdk.services.s3.model.CreateMultipartUploadRequest;
import software.amazon.awssdk.services.s3.model.CreateMultipartUploadResponse;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;
import software.amazon.awssdk.services.s3.model.UploadPartRequest;
import software.amazon.awssdk.services.s3.model.UploadPartResponse;
import vn.com.mbbank.kanban.mbmonitor.common.configs.S3Properties;
import vn.com.mbbank.kanban.mbmonitor.common.services.S3FileService;

/**
 * Implementation of S3FileService using AWS SDK v2.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class S3FileServiceImpl implements S3FileService {

  private final S3Properties s3Properties;

  private S3Client s3Client;


  @PostConstruct
  public void init() {
    s3Client = S3Client.builder()
        .credentialsProvider(StaticCredentialsProvider.create(
            AwsBasicCredentials.create(s3Properties.getAccessKey(), s3Properties.getSecretKey())))
        .endpointOverride(URI.create(s3Properties.getEndpoint()))
        .region(Region.of(s3Properties.getRegion()))
        .serviceConfiguration(S3Configuration.builder()
            .pathStyleAccessEnabled(true)
            .checksumValidationEnabled(false)
            .build())
        .build();
  }

  @Override
  public void upload(String key, byte[] content, String contentType) {
    s3Client.putObject(
        PutObjectRequest.builder()
            .bucket(s3Properties.getBucketName())
            .key(key)
            .contentType(contentType)
            .build(),
        RequestBody.fromBytes(content)
    );
  }

  @Override
  public byte[] download(String key) {
    return s3Client.getObjectAsBytes(
        GetObjectRequest.builder()
            .bucket(s3Properties.getBucketName())
            .key(key)
            .build()
    ).asByteArray();
  }

  @Override
  public void delete(String key) {
    s3Client.deleteObject(DeleteObjectRequest.builder()
        .bucket(s3Properties.getBucketName())
        .key(key)
        .build());
  }

  @Override
  public boolean exists(String key) {
    try {
      s3Client.headObject(HeadObjectRequest.builder()
          .bucket(s3Properties.getBucketName())
          .key(key)
          .build());
      return true;
    } catch (S3Exception e) {
      return false;
    }
  }

  @Override
  public S3Client getS3Client() {
    return s3Client;
  }

  @Override
  public String getBucketName() {
    return s3Properties.getBucketName();
  }


  @Override
  public String beginMultipartUpload(String key, String contentType) {
    CreateMultipartUploadRequest request = CreateMultipartUploadRequest.builder()
        .bucket(s3Properties.getBucketName())
        .key(key)
        .contentType(contentType)
        .build();

    CreateMultipartUploadResponse response = s3Client.createMultipartUpload(request);
    return response.uploadId();
  }

  @Override
  public void abortMultipartUpload(String key, String uploadId) {
    AbortMultipartUploadRequest abortRequest = AbortMultipartUploadRequest.builder()
        .bucket(s3Properties.getBucketName())
        .key(key)
        .uploadId(uploadId)
        .build();

    s3Client.abortMultipartUpload(abortRequest);
  }

  @Override
  public void completeMultipartUpload(String key, String uploadId, List<String> etags) {
    List<CompletedPart> completedParts = new ArrayList<>();
    for (int i = 0; i < etags.size(); i++) {
      completedParts.add(
          CompletedPart.builder()
              .partNumber(i + 1)
              .eTag(etags.get(i))
              .build()
      );
    }
    CompletedMultipartUpload completedMultipartUpload = CompletedMultipartUpload.builder()
        .parts(completedParts)
        .build();
    CompleteMultipartUploadRequest completeRequest = CompleteMultipartUploadRequest.builder()
        .bucket(s3Properties.getBucketName())
        .key(key)
        .uploadId(uploadId)
        .multipartUpload(completedMultipartUpload)
        .build();

    s3Client.completeMultipartUpload(completeRequest);
  }

  @Override
  public String uploadPart(String key, String uploadId, int partNumber, byte[] partBytes) {
    UploadPartRequest uploadPartRequest = UploadPartRequest.builder()
        .bucket(s3Properties.getBucketName())
        .key(key)
        .uploadId(uploadId)
        .partNumber(partNumber)
        .contentLength((long) partBytes.length)
        .build();

    UploadPartResponse response = s3Client.uploadPart(
        uploadPartRequest,
        RequestBody.fromBytes(partBytes)
    );

    return response.eTag();
  }


}
