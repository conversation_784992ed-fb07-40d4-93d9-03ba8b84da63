@echo off
echo Making JAR executable...

set MAIN_JAR=main\target\external-job-1.0-SNAPSHOT.jar
set TEMP_DIR=temp_manifest

if not exist "%MAIN_JAR%" (
    echo Error: JAR file not found: %MAIN_JAR%
    echo Please run: mvn clean package -DskipTests
    pause
    exit /b 1
)

echo Creating temporary directory...
if exist %TEMP_DIR% rmdir /s /q %TEMP_DIR%
mkdir %TEMP_DIR%
cd %TEMP_DIR%

echo Extracting JAR...
jar xf ..\%MAIN_JAR%

echo Creating MANIFEST.MF with Main-Class...
echo Manifest-Version: 1.0 > META-INF\MANIFEST.MF
echo Main-Class: vn.com.mbbank.kanban.mbmonitor.external.job.Main >> META-INF\MANIFEST.MF
echo Class-Path: . >> META-INF\MANIFEST.MF
echo. >> META-INF\MANIFEST.MF

echo Recreating JAR with new MANIFEST...
jar cfm ..\%MAIN_JAR% META-INF\MANIFEST.MF *

cd ..
rmdir /s /q %TEMP_DIR%

echo.
echo JAR is now executable!
echo.
echo To run with dependencies from Maven repository:
echo java -cp "%MAIN_JAR%;%USERPROFILE%\.m2\repository\vn\com\mbbank\kanban\mbmonitor\common\mbmonitor-common\1.0-SNAPSHOT\*" vn.com.mbbank.kanban.mbmonitor.external.job.Main
echo.
echo Or use run-simple.bat for easier execution
pause
