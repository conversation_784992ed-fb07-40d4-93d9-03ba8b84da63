@echo off
echo Creating MANIFEST.MF for executable JAR...

set JAR_FILE=main\target\external-job-1.0-SNAPSHOT.jar
set TEMP_DIR=temp_jar_extract

if not exist "%JAR_FILE%" (
    echo Error: JAR file not found: %JAR_FILE%
    echo Please run: mvn clean package -DskipTests
    pause
    exit /b 1
)

echo Extracting JAR...
mkdir %TEMP_DIR%
cd %TEMP_DIR%
jar xf ..\%JAR_FILE%

echo Creating new MANIFEST.MF...
mkdir META-INF 2>nul
echo Manifest-Version: 1.0 > META-INF\MANIFEST.MF
echo Main-Class: vn.com.mbbank.kanban.mbmonitor.external.job.Main >> META-INF\MANIFEST.MF
echo. >> META-INF\MANIFEST.MF

echo Recreating JAR with new MANIFEST...
jar cfm ..\%JAR_FILE% META-INF\MANIFEST.MF *

cd ..
rmdir /s /q %TEMP_DIR%

echo Done! JAR file updated with Main-Class in MANIFEST.MF
echo You can now run: java -jar %JAR_FILE%
pause
