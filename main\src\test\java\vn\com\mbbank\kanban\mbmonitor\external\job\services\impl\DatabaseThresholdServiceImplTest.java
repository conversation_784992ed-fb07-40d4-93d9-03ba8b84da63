package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import java.lang.reflect.UndeclaredThrowableException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.test.util.ReflectionTestUtils;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.exceptions.BusinessRuntimeException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.configs.QueryHikariDataSourceConfig;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SqlExecutionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseThresholdConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionOperatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonDatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRawAlertService;
import vn.com.mbbank.kanban.mbmonitor.common.services.DatabaseQueryService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForDev;
import vn.com.mbbank.kanban.mbmonitor.external.job.anotations.TestForUser;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.DatabaseThresholdRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertPriorityConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.AlertService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.DatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FilterAlertConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.MaintenanceTimeConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.ModifyAlertConfigService;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class DatabaseThresholdServiceImplTest {

  @InjectMocks
  private DatabaseThresholdServiceImpl databaseThresholdService;

  @Mock
  private DatabaseThresholdRepository databaseThresholdRepository;
  @Mock
  private DatabaseConnectionService databaseConnectionService;
  @Mock
  private CommonDatabaseConnectionService commonDatabaseConnectionService;
  @Mock
  private DatabaseQueryService superiorsService;
  @Mock
  private AlertService alertService;
  @Mock
  private CommonRawAlertService commonRawAlertService;
  @Mock
  private MaintenanceTimeConfigService maintenanceTimeConfigService;
  @Mock
  private FilterAlertConfigService filterAlertConfigService;
  @Mock
  private ModifyAlertConfigService modifyAlertConfigService;
  @Mock
  private AlertPriorityConfigService alertPriorityConfigService;
  @Mock
  QueryHikariDataSourceConfig queryHikariDataSourceConfig;
  @Mock
  SysLogKafkaProducerService sysLogKafkaProducerService;

  private DatabaseThresholdConfigEntity activeConfig;
  private DatabaseThresholdConfigEntity inactiveConfig;
  private DatabaseConnectionEntity connectionEntity;
  private DatabaseConnectionRequest connectionRequest;
  private SqlExecutionModel sqlExecutionModel;
  private AlertBaseModel alertBaseModel;

  @BeforeEach
  void setUp() {
    activeConfig = new DatabaseThresholdConfigEntity();
    activeConfig.setId("config1");
    activeConfig.setActive(true);
    activeConfig.setDatabaseConnectionId(1L);
    activeConfig.setSqlCommand("SELECT COUNT(*) FROM test_table");
    activeConfig.setConditionOperator(ConditionOperatorEnum.GREATER_THAN);
    activeConfig.setConditionValue(10L);
    activeConfig.setContent("Alert: Count is @count");
    activeConfig.setServiceId("service1");
    activeConfig.setApplicationId("app1");
    activeConfig.setRecipient("recipient1");
    activeConfig.setPriorityId(1L);

    inactiveConfig = new DatabaseThresholdConfigEntity();
    inactiveConfig.setId("config2");
    inactiveConfig.setActive(false);

    connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(1L);
    connectionEntity.setPassword("encryptedPassword");

    connectionRequest = new DatabaseConnectionRequest();
    connectionRequest.setPassword("decryptedPassword");

    SqlExecutionResponse.SqlMappingColumnData columnData = SqlExecutionResponse.SqlMappingColumnData.builder()
        .column("count(*)")
        .value("50")
        .build();
    SqlExecutionResponse.SqlDataMapping dataMapping = SqlExecutionResponse.SqlDataMapping.builder()
        .listSqlMappingColumnDatas(Collections.singletonList(columnData))
        .build();

    sqlExecutionModel = new SqlExecutionModel()
        .setListDataMappings(Collections.singletonList(dataMapping))
        .setListColumns(Collections.singletonList("count(*)"));
    alertBaseModel = new AlertBaseModel();
    alertBaseModel.setIsValid(true);
  }

  @TestForDev
  void getRepository_success() {
    JpaCommonRepository<DatabaseThresholdConfigEntity, String> repository = databaseThresholdService.getRepository();
    assertEquals(databaseThresholdRepository, repository);
  }

  @TestForUser
  void collect_throwsBusinessException_whenConfigNotFound() {
    when(databaseThresholdRepository.findById("config1")).thenReturn(Optional.empty());

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseThresholdService.collect("config1");
    });

    assertEquals(ErrorCode.DATABASE_COLLECT_IS_NOT_EXISTS.getMessage(), exception.getMessage());
    verify(databaseThresholdRepository).findById("config1");
  }

  @TestForUser
  void collect_throwsBusinessException_whenConfigInactive() {
    when(databaseThresholdRepository.findById("config2")).thenReturn(Optional.of(inactiveConfig));

    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseThresholdService.collect("config2");
    });

    assertEquals(ErrorCode.DATABASE_COLLECT_IS_INACTIVE.getMessage(), exception.getMessage());
    verify(databaseThresholdRepository).findById("config2");
  }



  @TestForUser
  void collect_conditionNotMet_returnsEarly() throws BusinessException, SQLException {
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    config.setSqlCommand("SELECT * FROM test");
    config.setConditionOperator(ConditionOperatorEnum.GREATER_THAN);
    config.setConditionValue(1L);
    config.setActive(true);
    when(databaseThresholdRepository.findById(any())).thenReturn(Optional.of(config));
    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(1L);
    connectionEntity.setPassword("encryptedPassword");
    connectionEntity.setIsActive(true);

    DatabaseConnectionRequest connectionRequest = new DatabaseConnectionRequest();
    connectionRequest.setId(null);
    connectionRequest.setPassword("decryptedPassword");

    HikariConfig hikariConfig = new HikariConfig();
    hikariConfig.setJdbcUrl("jdbc:test");

    HikariDataSource hikariDataSource = mock(HikariDataSource.class);
    Connection connection = mock(Connection.class);
    SqlExecutionResponse.SqlMappingColumnData columnData = SqlExecutionResponse.SqlMappingColumnData.builder()
        .column("count(*)")
        .value("50")
        .build();
    SqlExecutionResponse.SqlDataMapping dataMapping = SqlExecutionResponse.SqlDataMapping.builder()
        .listSqlMappingColumnDatas(Collections.singletonList(columnData))
        .build();
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(Collections.singletonList("col1"))
        .listDataMappings(List.of(dataMapping))
        .build();

    when(databaseConnectionService.findById(any())).thenReturn(connectionEntity);
    doNothing().when(databaseConnectionService).checkConnection(connectionRequest);
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(hikariConfig);
    when(queryHikariDataSourceConfig.getDataSource(anyString())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    when(superiorsService.executeQuery(any(),anyString(),any())).thenReturn(sqlExecutionResponse);
    databaseThresholdService.collect("123");
    verify(databaseConnectionService,times(1)).findById(any());
    verify(commonDatabaseConnectionService,times(1)).createHikariConfig(any());
    verify(queryHikariDataSourceConfig,times(1)).getDataSource(anyString());
    verify(superiorsService,times(1)).executeQuery(any(),anyString(),any());

  }

  @TestForUser
  void collect_conditionMetAndAlertsCreated_collectsAlerts() throws BusinessException, SQLException {
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    config.setSqlCommand("SELECT * FROM test");
    config.setConditionOperator(ConditionOperatorEnum.GREATER_THAN);
    config.setConditionValue(1L);
    config.setActive(true);
    config.setContent("123");
    when(databaseThresholdRepository.findById(any())).thenReturn(Optional.of(config));
    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(1L);
    connectionEntity.setPassword("encryptedPassword");
    connectionEntity.setIsActive(true);

    DatabaseConnectionRequest connectionRequest = new DatabaseConnectionRequest();
    connectionRequest.setId(null);
    connectionRequest.setPassword("decryptedPassword");

    HikariConfig hikariConfig = new HikariConfig();
    hikariConfig.setJdbcUrl("jdbc:test");

    HikariDataSource hikariDataSource = mock(HikariDataSource.class);
    Connection connection = mock(Connection.class);
    SqlExecutionResponse.SqlMappingColumnData columnData = SqlExecutionResponse.SqlMappingColumnData.builder()
        .column("count(*)")
        .value("50")
        .build();
    SqlExecutionResponse.SqlDataMapping dataMapping = SqlExecutionResponse.SqlDataMapping.builder()
        .listSqlMappingColumnDatas(Collections.singletonList(columnData))
        .build();
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(Collections.singletonList("col1"))
        .listDataMappings(List.of(dataMapping))
        .build();
    when(commonRawAlertService.createRawData(anyList(),any())).thenReturn(List.of(new AlertBaseModel()));
    when(databaseConnectionService.findById(any())).thenReturn(connectionEntity);
    doNothing().when(databaseConnectionService).checkConnection(connectionRequest);
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(hikariConfig);
    when(queryHikariDataSourceConfig.getDataSource(anyString())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    when(superiorsService.executeQuery(any(),anyString(),any())).thenReturn(sqlExecutionResponse);
    databaseThresholdService.collect("123");
    verify(databaseConnectionService,times(1)).findById(any());
    verify(commonDatabaseConnectionService,times(1)).createHikariConfig(any());
    verify(queryHikariDataSourceConfig,times(1)).getDataSource(anyString());
    verify(superiorsService,times(1)).executeQuery(any(),anyString(),any());
  }

  @TestForUser
  void collect_conditionMetAndNoAlertsCreated_doesNothing() throws SQLException, BusinessException {
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    config.setSqlCommand("SELECT * FROM test");
    config.setConditionOperator(ConditionOperatorEnum.LESS_THAN);
    config.setConditionValue(1L);
    config.setActive(true);
    when(databaseThresholdRepository.findById(any())).thenReturn(Optional.of(config));
    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(1L);
    connectionEntity.setPassword("encryptedPassword");

    DatabaseConnectionRequest connectionRequest = new DatabaseConnectionRequest();
    connectionRequest.setId(null);
    connectionRequest.setPassword("decryptedPassword");
    connectionEntity.setIsActive(true);

    HikariConfig hikariConfig = new HikariConfig();
    hikariConfig.setJdbcUrl("jdbc:test");

    HikariDataSource hikariDataSource = mock(HikariDataSource.class);
    Connection connection = mock(Connection.class);
    SqlExecutionResponse.SqlMappingColumnData columnData = SqlExecutionResponse.SqlMappingColumnData.builder()
        .column("count(*)")
        .value("50")
        .build();
    SqlExecutionResponse.SqlDataMapping dataMapping = SqlExecutionResponse.SqlDataMapping.builder()
        .listSqlMappingColumnDatas(Collections.singletonList(columnData))
        .build();
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(Collections.singletonList("col1"))
        .listDataMappings(List.of(dataMapping))
        .build();

    when(databaseConnectionService.findById(any())).thenReturn(connectionEntity);
    doNothing().when(databaseConnectionService).checkConnection(connectionRequest);
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(hikariConfig);
    when(queryHikariDataSourceConfig.getDataSource(anyString())).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    when(superiorsService.executeQuery(any(),anyString(),any())).thenReturn(sqlExecutionResponse);
    databaseThresholdService.collect("123");
    verify(databaseConnectionService,times(1)).findById(any());
    verify(commonDatabaseConnectionService,times(1)).createHikariConfig(any());
    verify(queryHikariDataSourceConfig,times(1)).getDataSource(anyString());
    verify(superiorsService,times(1)).executeQuery(any(),anyString(),any());
  }

  @TestForUser
  void collect_executeSqlThrowsBusinessException() {
    when(databaseThresholdRepository.findById("config1")).thenReturn(Optional.of(activeConfig));

     assertThrows(BusinessException.class, () -> {
      databaseThresholdService.collect("config1");
    });

    verify(databaseThresholdRepository).findById("config1");
  }


  @TestForDev
  void executeSql_success() throws BusinessException, SQLException {
    // Arrange
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    config.setSqlCommand("SELECT * FROM test");

    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(1L);
    connectionEntity.setPassword("encryptedPassword");
    connectionEntity.setIsActive(true);

    DatabaseConnectionRequest connectionRequest = new DatabaseConnectionRequest();
    connectionRequest.setId(null);
    connectionRequest.setPassword("decryptedPassword");

    HikariConfig hikariConfig = new HikariConfig();
    hikariConfig.setJdbcUrl("jdbc:test");

    HikariDataSource hikariDataSource = mock(HikariDataSource.class);
    Connection connection = mock(Connection.class);
    SqlExecutionResponse sqlExecutionResponse = SqlExecutionResponse.builder()
        .listColumns(Collections.singletonList("col1"))
        .listDataMappings(Collections.emptyList())
        .build();

    when(databaseConnectionService.findById(1L)).thenReturn(connectionEntity);
    doNothing().when(databaseConnectionService).checkConnection(connectionRequest);
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(hikariConfig);
    when(queryHikariDataSourceConfig.getDataSource("jdbc:test")).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    when(superiorsService.executeQuery(connection, "SELECT * FROM test", true)).thenReturn(sqlExecutionResponse);

    // Act
    SqlExecutionModel result = ReflectionTestUtils.invokeMethod(databaseThresholdService, "executeSql", config);

    // Assert
    assertNotNull(result);
    assertEquals(sqlExecutionResponse.getListColumns(), result.getListColumns());
    assertEquals(sqlExecutionResponse.getListDataMappings(), result.getListDataMappings());

    verify(databaseConnectionService).findById(1L);
    verify(commonDatabaseConnectionService).createHikariConfig(any());
    verify(queryHikariDataSourceConfig).getDataSource("jdbc:test");
    verify(hikariDataSource).getConnection();
    verify(superiorsService).executeQuery(connection, "SELECT * FROM test", true);
  }

  @TestForDev
  void executeSql_getDataSourceThrowsRuntimeException() throws BusinessException, SQLException {
    // Arrange
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    config.setSqlCommand("SELECT * FROM test");

    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(1L);
    connectionEntity.setPassword("encryptedPassword");
    connectionEntity.setIsActive(true);
    DatabaseConnectionRequest connectionRequest = new DatabaseConnectionRequest();
    connectionRequest.setId(null);
    connectionRequest.setPassword("decryptedPassword");

    HikariConfig hikariConfig = new HikariConfig();
    hikariConfig.setJdbcUrl("jdbc:test");

    when(databaseConnectionService.findById(1L)).thenReturn(connectionEntity);
    doNothing().when(databaseConnectionService).checkConnection(connectionRequest);
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(hikariConfig);
    when(queryHikariDataSourceConfig.getDataSource("jdbc:test")).thenThrow(new RuntimeException("Test Runtime Exception"));

    // Act & Assert
    BusinessRuntimeException exception = assertThrows(BusinessRuntimeException.class, () -> {
      ReflectionTestUtils.invokeMethod(databaseThresholdService, "executeSql", config);
    });

    assertEquals(ErrorCode.DATABASE_CONNECT_FALSE.getMessage(), exception.getMessage());

    verify(databaseConnectionService).findById(1L);
  }

  @TestForDev
  void executeSql_getDataSourceThrowsSQLException() throws BusinessException, SQLException {
    // Arrange
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    config.setSqlCommand("SELECT * FROM test");

    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(1L);
    connectionEntity.setPassword("encryptedPassword");
    connectionEntity.setIsActive(true);

    DatabaseConnectionRequest connectionRequest = new DatabaseConnectionRequest();
    connectionRequest.setId(null);
    connectionRequest.setPassword("decryptedPassword");

    HikariConfig hikariConfig = new HikariConfig();
    hikariConfig.setJdbcUrl("jdbc:test");
    when(databaseConnectionService.findById(1L)).thenReturn(connectionEntity);
    doNothing().when(databaseConnectionService).checkConnection(connectionRequest);
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(hikariConfig);
    HikariDataSource dataSource = mock(HikariDataSource.class);
    when(queryHikariDataSourceConfig.getDataSource(anyString())).thenReturn(dataSource);
    when(dataSource.getConnection()).thenThrow(new SQLException("Test SQL Exception"));

    // Act & Assert
    BusinessRuntimeException exception = assertThrows(BusinessRuntimeException.class, () -> {
      ReflectionTestUtils.invokeMethod(databaseThresholdService, "executeSql", config);
    });

    assertEquals(ErrorCode.DATABASE_CONNECT_FALSE.getMessage(), exception.getMessage());

    verify(databaseConnectionService).findById(1L);
  }
  @TestForDev
  void executeSql_getDataSourceThrowsDatabaseConnectionIsInvalidException() throws BusinessException, SQLException {
    // Arrange
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    config.setSqlCommand("SELECT * FROM test");

    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(1L);
    connectionEntity.setPassword("encryptedPassword");
    connectionEntity.setIsActive(false);

    DatabaseConnectionRequest connectionRequest = new DatabaseConnectionRequest();
    connectionRequest.setId(null);
    connectionRequest.setPassword("decryptedPassword");

    HikariConfig hikariConfig = new HikariConfig();
    hikariConfig.setJdbcUrl("jdbc:test");
    when(databaseConnectionService.findById(1L)).thenReturn(connectionEntity);
    doNothing().when(databaseConnectionService).checkConnection(connectionRequest);
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(hikariConfig);
    HikariDataSource dataSource = mock(HikariDataSource.class);
    when(queryHikariDataSourceConfig.getDataSource(anyString())).thenReturn(dataSource);
    when(dataSource.getConnection()).thenThrow(new SQLException("Test SQL Exception"));

    // Act & Assert
    assertThrows(UndeclaredThrowableException.class, () -> {
      ReflectionTestUtils.invokeMethod(databaseThresholdService, "executeSql", config);
    });
    verify(databaseConnectionService).findById(1L);
  }

  @TestForDev
  void executeSql_getConnectionThrowsSQLException() throws BusinessException, SQLException {
    // Arrange
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    config.setSqlCommand("SELECT * FROM test");

    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(1L);
    connectionEntity.setPassword("encryptedPassword");
    connectionEntity.setIsActive(true);

    DatabaseConnectionRequest connectionRequest = new DatabaseConnectionRequest();
    connectionRequest.setId(null);
    connectionRequest.setPassword("decryptedPassword");

    HikariConfig hikariConfig = new HikariConfig();
    hikariConfig.setJdbcUrl("jdbc:test");

    HikariDataSource hikariDataSource = mock(HikariDataSource.class);

    when(databaseConnectionService.findById(1L)).thenReturn(connectionEntity);
    doNothing().when(databaseConnectionService).checkConnection(connectionRequest);
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(hikariConfig);
    when(queryHikariDataSourceConfig.getDataSource("jdbc:test")).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenThrow(new SQLException("Test SQL Exception"));

    // Act & Assert
    BusinessRuntimeException exception = assertThrows(BusinessRuntimeException.class, () -> {
      ReflectionTestUtils.invokeMethod(databaseThresholdService, "executeSql", config);
    });

    assertEquals(ErrorCode.DATABASE_CONNECT_FALSE.getMessage(), exception.getMessage());

    verify(databaseConnectionService).findById(1L);
    verify(commonDatabaseConnectionService,times(1)).createHikariConfig(any());
  }

  @TestForDev
  void executeSql_executeQueryThrowsSQLException() throws BusinessException, SQLException {
    // Arrange
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    config.setSqlCommand("SELECT * FROM test");

    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(1L);
    connectionEntity.setPassword("encryptedPassword");
    connectionEntity.setIsActive(true);

    DatabaseConnectionRequest connectionRequest = new DatabaseConnectionRequest();
    connectionRequest.setId(null);
    connectionRequest.setPassword("decryptedPassword");

    HikariConfig hikariConfig = new HikariConfig();
    hikariConfig.setJdbcUrl("jdbc:test");

    HikariDataSource hikariDataSource = mock(HikariDataSource.class);
    Connection connection = mock(Connection.class);

    when(databaseConnectionService.findById(1L)).thenReturn(connectionEntity);
    doNothing().when(databaseConnectionService).checkConnection(connectionRequest);
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(hikariConfig);
    when(queryHikariDataSourceConfig.getDataSource("jdbc:test")).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    when(superiorsService.executeQuery(connection, "SELECT * FROM test", true)).thenThrow(new SQLException("Test SQL Exception"));

    // Act & Assert
    BusinessRuntimeException exception = assertThrows(BusinessRuntimeException.class, () -> {
      ReflectionTestUtils.invokeMethod(databaseThresholdService, "executeSql", config);
    });

    assertEquals(ErrorCode.DATABASE_CONNECT_FALSE.getMessage(), exception.getMessage());

    verify(databaseConnectionService).findById(1L);
    verify(commonDatabaseConnectionService,times(1)).createHikariConfig(any());
    verify(superiorsService).executeQuery(connection, "SELECT * FROM test", true);
  }

  @TestForDev
  void executeSql_executeQueryThrowsRuntimeException() throws BusinessException, SQLException {
    // Arrange
    DatabaseThresholdConfigEntity config = new DatabaseThresholdConfigEntity();
    config.setDatabaseConnectionId(1L);
    config.setSqlCommand("SELECT * FROM test");

    DatabaseConnectionEntity connectionEntity = new DatabaseConnectionEntity();
    connectionEntity.setId(1L);
    connectionEntity.setPassword("encryptedPassword");

    DatabaseConnectionRequest connectionRequest = new DatabaseConnectionRequest();
    connectionRequest.setId(null);
    connectionRequest.setPassword("decryptedPassword");
    connectionEntity.setIsActive(true);

    HikariConfig hikariConfig = new HikariConfig();
    hikariConfig.setJdbcUrl("jdbc:test");

    HikariDataSource hikariDataSource = mock(HikariDataSource.class);
    Connection connection = mock(Connection.class);

    when(databaseConnectionService.findById(1L)).thenReturn(connectionEntity);
    doNothing().when(databaseConnectionService).checkConnection(connectionRequest);
    when(commonDatabaseConnectionService.createHikariConfig(any())).thenReturn(hikariConfig);
    when(queryHikariDataSourceConfig.getDataSource("jdbc:test")).thenReturn(hikariDataSource);
    when(hikariDataSource.getConnection()).thenReturn(connection);
    when(superiorsService.executeQuery(connection, "SELECT * FROM test", true)).thenThrow(new RuntimeException("Test Runtime Exception"));

    // Act & Assert
    BusinessRuntimeException exception = assertThrows(BusinessRuntimeException.class, () -> {
      ReflectionTestUtils.invokeMethod(databaseThresholdService, "executeSql", config);
    });

    assertEquals(ErrorCode.DATABASE_CONNECT_FALSE.getMessage(), exception.getMessage());

    verify(databaseConnectionService).findById(1L);
    verify(commonDatabaseConnectionService,times(1)).createHikariConfig(any());
    verify(queryHikariDataSourceConfig).getDataSource("jdbc:test");
    verify(superiorsService).executeQuery(connection, "SELECT * FROM test", true);
  }

  @TestForDev
  void findAllByActiveTrue_success() {
    List<DatabaseThresholdConfigEntity> expectedList = Arrays.asList(activeConfig);
    when(databaseThresholdRepository.findAllByActiveTrue()).thenReturn(expectedList);

    List<DatabaseThresholdConfigEntity> result = databaseThresholdService.findAllByActiveTrue();

    assertEquals(expectedList, result);
    verify(databaseThresholdRepository).findAllByActiveTrue();
  }

  @TestForDev
  void filterAlerts_success() {
    List<AlertBaseModel> inputAlerts = Arrays.asList(alertBaseModel);
    List<AlertBaseModel> expectedAlerts = Arrays.asList(alertBaseModel);
    when(filterAlertConfigService.updateAlertsForFilter(inputAlerts)).thenReturn(expectedAlerts);

    List<AlertBaseModel> result = databaseThresholdService.filterAlerts(inputAlerts);

    assertEquals(expectedAlerts, result);
    verify(filterAlertConfigService).updateAlertsForFilter(inputAlerts);
  }

  @TestForDev
  void modifyAlerts_success() {
    List<AlertBaseModel> inputAlerts = Arrays.asList(alertBaseModel);
    List<AlertBaseModel> expectedAlerts = Arrays.asList(alertBaseModel);
    when(modifyAlertConfigService.updateAlertsForModify(inputAlerts)).thenReturn(expectedAlerts);

    List<AlertBaseModel> result = databaseThresholdService.modifyAlerts(inputAlerts);

    assertEquals(expectedAlerts, result);
    verify(modifyAlertConfigService).updateAlertsForModify(inputAlerts);
  }

  @TestForDev
  void maintenanceAlerts_success() {
    List<AlertBaseModel> inputAlerts = Arrays.asList(alertBaseModel);
    List<AlertBaseModel> expectedAlerts = Arrays.asList(alertBaseModel);
    when(maintenanceTimeConfigService.updateAlertsForMaintenance(inputAlerts)).thenReturn(expectedAlerts);

    List<AlertBaseModel> result = databaseThresholdService.maintenanceAlerts(inputAlerts);

    assertEquals(expectedAlerts, result);
    verify(maintenanceTimeConfigService).updateAlertsForMaintenance(inputAlerts);
  }

  @TestForUser
  void saveAlerts_success() {
    List<AlertBaseModel> inputAlerts = Arrays.asList(alertBaseModel);
    List<AlertEntity> savedEntities = Collections.singletonList(new AlertEntity());
      when(alertService.saveAll(any())).thenReturn(savedEntities);
      List<AlertBaseModel> result = databaseThresholdService.saveAlerts(inputAlerts);
      assertNotNull(result);
  }

  @TestForUser
  void collectFilters_success_filtersInvalidAlerts() {
    AlertBaseModel validAlert = new AlertBaseModel();
    validAlert.setIsValid(true);
    AlertBaseModel invalidAlert = new AlertBaseModel();
    invalidAlert.setIsValid(false);
    List<AlertBaseModel> inputAlerts = Arrays.asList(validAlert, invalidAlert);

    List<AlertBaseModel> result = databaseThresholdService.collectFilters(inputAlerts);

    assertEquals(1, result.size());
    assertTrue(result.contains(validAlert));
    assertFalse(result.contains(invalidAlert));
  }

  @TestForUser
  void createAlerts_success() {
      AlertPriorityConfigEntity priorityEntity = new AlertPriorityConfigEntity();
      priorityEntity.setName("High");
      when(alertPriorityConfigService.findById(1L)).thenReturn(priorityEntity);

      List<AlertBaseModel> rawAlerts = Collections.singletonList(alertBaseModel);
      when(commonRawAlertService.createRawData(anyList(), any(AlertSourceTypeEnum.class))).thenReturn(rawAlerts);

      List<AlertBaseModel> result = databaseThresholdService.createAlerts(activeConfig, 15L,12L);

      assertNotNull(result);
      assertFalse(result.isEmpty());
      verify(alertPriorityConfigService).findById(1L);
      verify(commonRawAlertService).createRawData(anyList(), any(AlertSourceTypeEnum.class));
  }
  @TestForUser
  void createAlerts_success_priorityNull() {
    AlertPriorityConfigEntity priorityEntity = new AlertPriorityConfigEntity();
    priorityEntity.setName("High");
    when(alertPriorityConfigService.findById(1L)).thenReturn(null);

    List<AlertBaseModel> rawAlerts = Collections.singletonList(alertBaseModel);
    when(commonRawAlertService.createRawData(anyList(), any(AlertSourceTypeEnum.class))).thenReturn(rawAlerts);

    List<AlertBaseModel> result = databaseThresholdService.createAlerts(activeConfig, 15L,12L);

    assertNotNull(result);
    assertFalse(result.isEmpty());
    verify(alertPriorityConfigService).findById(1L);
    verify(commonRawAlertService).createRawData(anyList(), any(AlertSourceTypeEnum.class));
  }
  @TestForUser
  void createAlerts_returnsNull_whenContentIsEmpty() {
    activeConfig.setContent("");
      List<AlertBaseModel> result = databaseThresholdService.createAlerts(activeConfig, 15L,12L);
      assertEquals(null, result);
      verify(commonRawAlertService, never()).createRawData(anyList(), any(AlertSourceTypeEnum.class));
  }

  @TestForUser
  void checkCondition_greaterThan_success() {

    assertTrue(databaseThresholdService.checkCondition(15L, activeConfig));
    assertFalse(databaseThresholdService.checkCondition(10L, activeConfig));
    assertFalse(databaseThresholdService.checkCondition(5L, activeConfig));
  }

  @TestForUser
  void checkCondition_greaterThanOrEqual_success() {
    activeConfig.setConditionOperator(ConditionOperatorEnum.GREATER_THAN_OR_EQUAL);
    assertTrue(databaseThresholdService.checkCondition(15L, activeConfig));
    assertTrue(databaseThresholdService.checkCondition(10L, activeConfig));
    assertFalse(databaseThresholdService.checkCondition(5L, activeConfig));
  }

  @TestForUser
  void checkCondition_equal_success() {
    activeConfig.setConditionOperator(ConditionOperatorEnum.EQUAL);
    assertTrue(databaseThresholdService.checkCondition(10L, activeConfig));
    assertFalse(databaseThresholdService.checkCondition(15L, activeConfig));
  }

  @TestForUser
  void checkCondition_lessThan_success() {
    activeConfig.setConditionOperator(ConditionOperatorEnum.LESS_THAN);
    assertTrue(databaseThresholdService.checkCondition(5L, activeConfig));
    assertFalse(databaseThresholdService.checkCondition(10L, activeConfig));
    assertFalse(databaseThresholdService.checkCondition(15L, activeConfig));
  }

  @TestForUser
  void checkCondition_lessThanOrEqual_success() {
    activeConfig.setConditionOperator(ConditionOperatorEnum.LESS_THAN_OR_EQUAL);
    assertTrue(databaseThresholdService.checkCondition(5L, activeConfig));
    assertTrue(databaseThresholdService.checkCondition(10L, activeConfig));
    assertFalse(databaseThresholdService.checkCondition(15L, activeConfig));
  }

  @TestForUser
  void getCountValue_success() throws BusinessException {
    Long value = databaseThresholdService.getCountValue(sqlExecutionModel);
    assertEquals(50L, value);
  }

  @TestForUser
  void getCountValue_throwsBusinessException_whenListDataMappingsIsNull() {
    sqlExecutionModel.setListDataMappings(null);
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseThresholdService.getCountValue(sqlExecutionModel);
    });
    assertEquals(ErrorCode.DATABASE_THRESHOLD_SQL_RESULT_IS_EMPTY.getMessage(), exception.getMessage());
  }

  @TestForUser
  void getCountValue_throwsBusinessException_whenListDataMappingsIsEmpty() {
    sqlExecutionModel.setListDataMappings(Collections.emptyList());
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseThresholdService.getCountValue(sqlExecutionModel);
    });
    assertEquals(ErrorCode.DATABASE_THRESHOLD_SQL_RESULT_IS_EMPTY.getMessage(), exception.getMessage());
  }

  @TestForUser
  void getCountValue_throwsBusinessException_whenListSqlMappingColumnDatasIsNull() {
    SqlExecutionResponse.SqlDataMapping dataMapping = SqlExecutionResponse.SqlDataMapping.builder()
        .listSqlMappingColumnDatas(null)
        .build();

    SqlExecutionModel model = new SqlExecutionModel()
        .setListDataMappings(Collections.singletonList(dataMapping))
        .setListColumns(Collections.singletonList("count(*)"));
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseThresholdService.getCountValue(model);
    });
    assertEquals(ErrorCode.DATABASE_THRESHOLD_SQL_RESULT_IS_EMPTY.getMessage(), exception.getMessage());
  }

  @TestForUser
  void getCountValue_throwsBusinessException_whenListSqlMappingColumnDatasIsEmpty() {

    SqlExecutionResponse.SqlDataMapping dataMapping = SqlExecutionResponse.SqlDataMapping.builder()
        .listSqlMappingColumnDatas(Collections.emptyList())
        .build();

    SqlExecutionModel model = new SqlExecutionModel()
        .setListDataMappings(Collections.singletonList(dataMapping))
        .setListColumns(Collections.singletonList("count(*)"));
    BusinessException exception = assertThrows(BusinessException.class, () -> {
      databaseThresholdService.getCountValue(model);
    });
    assertEquals(ErrorCode.DATABASE_THRESHOLD_SQL_RESULT_IS_EMPTY.getMessage(), exception.getMessage());
  }

  @TestForUser
  void getCountValue_throwsNumberFormatException_whenValueIsNotLong() {
    SqlExecutionResponse.SqlMappingColumnData columnData = SqlExecutionResponse.SqlMappingColumnData.builder()
        .column("count(*)")
        .value("abc")
        .build();
    SqlExecutionResponse.SqlDataMapping dataMapping = SqlExecutionResponse.SqlDataMapping.builder()
        .listSqlMappingColumnDatas(Collections.singletonList(columnData))
        .build();
    sqlExecutionModel.setListDataMappings(Collections.singletonList(dataMapping));

    assertThrows(NumberFormatException.class, () -> {
      databaseThresholdService.getCountValue(sqlExecutionModel);
    });
  }
}