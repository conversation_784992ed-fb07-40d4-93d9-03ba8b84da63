package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.core.services.common.BaseSoftServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.FileStorageRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FileStorageService;

/**
 * Service Logic FileStorageEntity service.
 */
@Service
@RequiredArgsConstructor
public class FileStorageServiceImpl extends BaseSoftServiceImpl<FileStorageEntity, Long>
    implements FileStorageService {
  private final FileStorageRepository fileStorageRepository;

  private static final Long EXPORT_DATA_FILE_TIMEOUT_MS =  8 * 24 * 60 * 60 * 1000L; //millisecond
  private final Logger logger = LoggerFactory.getLogger(FileStorageServiceImpl.class);

  public FileStorageEntity registerFile(String filePath, String dependencyName, String dependencyId)
      throws BusinessException {
    File file = new File(filePath);
    if (!file.exists() || !file.isFile()) {
      logger.error(" File not found" + filePath);
      throw new BusinessException(ErrorCode.EXPORT_DATA_NOT_FOUND);
    }
    FileStorageEntity entity = new FileStorageEntity();
    entity.setPath(filePath);
    entity.setDependencyName(dependencyName);
    entity.setDependencyId(dependencyId);
    return save(entity);
  }

  @Override
  public List<FileStorageEntity> findByIdIn(List<Long> ids) {
    return fileStorageRepository.findByIdIn(ids);
  }

  @Override
  public List<FileStorageEntity> findByIdNotInAndDependencyName(List<Long> ids, String dependencyName) {
    return fileStorageRepository.findAllByIdNotInAndDependencyName(ids, dependencyName);
  }

  @Override
  public void deleteExpiredFile(String folder, List<String> filePathsToDelete) {
    // delete file by list path on database
    for (String filePathStr : filePathsToDelete) {
      try {
        Path filePath = Paths.get(filePathStr).toAbsolutePath().normalize();
        if (Files.exists(filePath)) {
          Files.delete(filePath);
          logger.error("Deleted file from list: " + filePath);
        }
      } catch (IOException e) {
        logger.error("Error deleting file from list " + filePathStr + ": " + e.getMessage());
      }
    }
    // delete file expired time in NFS which exist in NFS but not save in database
    Path baseDir = Paths.get(folder).toAbsolutePath().normalize();
    long currentTime = System.currentTimeMillis();

    try {
      Files.walk(baseDir)
          .filter(Files::isRegularFile)
          .forEach(filePath -> {
            File file = filePath.toFile();
            if (currentTime - file.lastModified() > EXPORT_DATA_FILE_TIMEOUT_MS) {
              try {
                Files.delete(filePath);
                logger.error("Deleted file older than expired days: " + filePath);
              } catch (IOException e) {
                logger.error("Error deleting file " + filePath + ": " + e.getMessage());
              }
            }
          });
    } catch (IOException e) {
      logger.error("Error walking directory " + baseDir + ": " + e.getMessage());
    }
  }

  @Override
  protected BaseSoftRepository<FileStorageEntity, Long> getRepository() {
    return fileStorageRepository;
  }
}
