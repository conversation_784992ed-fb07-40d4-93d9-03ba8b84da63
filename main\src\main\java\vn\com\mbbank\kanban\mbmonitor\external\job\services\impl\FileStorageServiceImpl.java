package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response;
import software.amazon.awssdk.services.s3.model.S3Object;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.core.services.common.BaseSoftServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.S3FileService;
import vn.com.mbbank.kanban.mbmonitor.external.job.repositories.FileStorageRepository;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.FileStorageService;

@Service
@RequiredArgsConstructor
public class FileStorageServiceImpl extends BaseSoftServiceImpl<FileStorageEntity, Long>
    implements FileStorageService {

  private final FileStorageRepository fileStorageRepository;
  private final S3FileService s3FileService;
  private final Logger logger = LoggerFactory.getLogger(FileStorageServiceImpl.class);

  @Override
  public FileStorageEntity registerFile(String filePath, String dependencyName, String dependencyId)
      throws BusinessException {
    try {
      if (!s3FileService.exists(filePath)) {
        logger.error("S3 file not found: {}", filePath);
        throw new BusinessException(ErrorCode.EXPORT_DATA_NOT_FOUND);
      }

      FileStorageEntity entity = new FileStorageEntity();
      entity.setPath(filePath);
      entity.setDependencyName(dependencyName);
      entity.setDependencyId(dependencyId);
      return save(entity);
    } catch (Exception e) {
      logger.error("Error registering S3 file: {}", filePath, e);
      throw new BusinessException(ErrorCode.EXPORT_DATA_NOT_FOUND);
    }
  }

  @Override
  public List<FileStorageEntity> findByIdIn(List<Long> ids) {
    return fileStorageRepository.findByIdIn(ids);
  }

  @Override
  public List<FileStorageEntity> findByIdNotInAndDependencyName(List<Long> ids, String dependencyName) {
    return fileStorageRepository.findAllByIdNotInAndDependencyName(ids, dependencyName);
  }

  @Override
  public void deleteExpiredFile(String folder, List<String> filePathsToDelete) {
    // 1. Delete by explicit file paths
    for (String key : filePathsToDelete) {
      try {
        if (s3FileService.exists(key)) {
          s3FileService.delete(key);
          logger.info("Deleted file from S3 (list): {}", key);
        }
      } catch (Exception e) {
        logger.warn("Failed to delete file from S3 (list): {} - {}", key, e.getMessage());
      }
    }

    // 2. Delete expired files in S3 (older than threshold)
    long now = System.currentTimeMillis();
    long timeoutMillis = 8L * 24 * 60 * 60 * 1000;

    try {
      ListObjectsV2Request request = ListObjectsV2Request.builder()
          .bucket(s3FileService.getBucketName())
          .prefix(folder)
          .build();

      ListObjectsV2Response response;
      String continuationToken = null;

      do {
        if (continuationToken != null) {
          request = request.toBuilder().continuationToken(continuationToken).build();
        }
        response = s3FileService.getS3Client().listObjectsV2(request);

        for (S3Object obj : response.contents()) {
          if (now - obj.lastModified().toEpochMilli() > timeoutMillis) {
            try {
              s3FileService.delete(obj.key());
              logger.info("Deleted expired file from S3: {}", obj.key());
            } catch (Exception e) {
              logger.warn("Error deleting expired file {}: {}", obj.key(), e.getMessage());
            }
          }
        }

        continuationToken = response.nextContinuationToken();
      } while (response.isTruncated());

    } catch (Exception e) {
      logger.error("Failed to scan and delete expired S3 files under prefix '{}': {}", folder, e.getMessage());
    }
  }


  @Override
  protected BaseSoftRepository<FileStorageEntity, Long> getRepository() {
    return fileStorageRepository;
  }
}
