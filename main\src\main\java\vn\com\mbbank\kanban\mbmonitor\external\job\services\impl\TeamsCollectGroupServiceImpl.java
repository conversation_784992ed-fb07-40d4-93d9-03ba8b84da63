package vn.com.mbbank.kanban.mbmonitor.external.job.services.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupUserEntity;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.TeamsConfigModelToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonTeamsService;
import vn.com.mbbank.kanban.mbmonitor.common.services.builder.CommonTeamsServiceBuilder;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TeamsCollectGroupService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TeamsConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TeamsGroupConfigService;
import vn.com.mbbank.kanban.mbmonitor.external.job.services.TeamsGroupUserService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 5/7/2025
 */
@Service
@RequiredArgsConstructor
public class TeamsCollectGroupServiceImpl implements TeamsCollectGroupService {
  private final TeamsGroupConfigService teamsGroupConfigService;
  private final TeamsGroupUserService teamsGroupUserService;
  private final TeamsConfigService teamsConfigService;

  @Override
  @Transactional
  public void collectGroup() {
    var config = teamsConfigService.findAlertConfig().orElse(null);
    if (KanbanCommonUtil.isEmpty(config)) {
      return;
    }
    List<TeamsGroupConfigEntity> teamsGroupConfigEntities = new ArrayList<>();
    List<String> teamsGroups = new ArrayList<>();
    List<TeamsGroupUserEntity> teamsGroupUserEntities = new ArrayList<>();
    Map<String, List<TeamsGroupUserEntity>> teamsGroupUserEntityMap = new HashMap<>();
    var configModel = TeamsConfigModelToEntityMapper.INSTANCE.map(config);
    configModel.setClientSecret(KanbanEncryptorUtils.decrypt(config.getClientSecret()));
    configModel.setPassword(KanbanEncryptorUtils.decrypt(config.getPassword()));
    CommonTeamsService commonTeamsService = new CommonTeamsServiceBuilder(configModel).getInstance();
    var allGroupChats = commonTeamsService.getAllGroupChat();
    int position = 1;
    for (var group : allGroupChats) {
      TeamsGroupConfigEntity teamsGroupConfigEntity = new TeamsGroupConfigEntity();
      teamsGroupConfigEntity.setTeamsConfigId(config.getId());
      teamsGroupConfigEntity.setTeamsGroupId(group.getGroupChatId());
      teamsGroupConfigEntity.setPosition(position++);
      teamsGroupConfigEntity.setTeamsGroupName(group.getGroupChatName());
      teamsGroupConfigEntities.add(teamsGroupConfigEntity);
      teamsGroups.add(group.getGroupChatId());
      // create user in group config
      List<TeamsGroupUserEntity> groupUserEntities = new ArrayList<>();
      for (var email : group.getEmailValid()) {
        TeamsGroupUserEntity teamsGroupUserEntity = new TeamsGroupUserEntity();
        teamsGroupUserEntity.setTeamsGroupChatId(group.getGroupChatId());
        teamsGroupUserEntity.setEmail(email);
        groupUserEntities.add(teamsGroupUserEntity);
      }
      teamsGroupUserEntityMap.put(group.getGroupChatId(), groupUserEntities);
    }

    teamsGroupConfigService.deleteAllByTeamsConfigId(config.getId());
    teamsGroupUserService.deleteAllByTeamsGroupChatIdIn(teamsGroups);
    var groupSaves = teamsGroupConfigService.saveAll(teamsGroupConfigEntities);

    for (var group : groupSaves) {
      if (teamsGroupUserEntityMap.containsKey(group.getTeamsGroupId())) {
        var userEntities = teamsGroupUserEntityMap.get(group.getTeamsGroupId());
        var userEntitiesMapGroupId = userEntities.stream().map(user -> {
          user.setTeamsGroupId(group.getId());
          return user;
        }).toList();
        teamsGroupUserEntities.addAll(userEntitiesMapGroupId);
      }
    }
    teamsGroupUserService.saveAll(teamsGroupUserEntities);
  }
}
