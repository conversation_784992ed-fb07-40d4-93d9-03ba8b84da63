package vn.com.mbbank.kanban.mbmonitor.common.services;

import org.springframework.scheduling.annotation.Async;
import vn.com.mbbank.kanban.mbmonitor.common.constants.BeanNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationRequest;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/19/2025
 */
public interface PushNotificationService {

  /**
   * push notification.
   *
   * @param notificationRequest notificationRequest
   */
  @Async(BeanNameConstants.COMMON_TASK_EXECUTOR)
  void push(NotificationRequest notificationRequest);
}
