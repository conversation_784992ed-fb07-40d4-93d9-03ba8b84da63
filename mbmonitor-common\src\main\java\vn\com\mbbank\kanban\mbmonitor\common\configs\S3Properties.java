package vn.com.mbbank.kanban.mbmonitor.common.configs;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties mapped from the {@code s3.*} prefix.
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "s3")
public class S3Properties {
  private String accessKey;
  private String secretKey;
  private String endpoint;
  private String bucketName;
  private String region = "ap-southeast-1";
}
