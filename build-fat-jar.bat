@echo off
setlocal enabledelayedexpansion

echo Building fat JAR manually...

set MAIN_JAR=main\target\external-job-1.0-SNAPSHOT.jar
set FAT_JAR=main\target\external-job-1.0-SNAPSHOT-fat.jar
set TEMP_DIR=temp_fat_jar
set M2_REPO=%USERPROFILE%\.m2\repository

if not exist "%MAIN_JAR%" (
    echo Error: Main JAR not found: %MAIN_JAR%
    echo Please run: mvn clean package -DskipTests
    pause
    exit /b 1
)

echo Creating temporary directory...
if exist %TEMP_DIR% rmdir /s /q %TEMP_DIR%
mkdir %TEMP_DIR%
cd %TEMP_DIR%

echo Extracting main JAR...
jar xf ..\%MAIN_JAR%

echo Extracting dependencies...
REM Spring Boot dependencies
if exist "%M2_REPO%\org\springframework\boot\spring-boot-starter\3.3.10\spring-boot-starter-3.3.10.jar" (
    jar xf "%M2_REPO%\org\springframework\boot\spring-boot-starter\3.3.10\spring-boot-starter-3.3.10.jar"
)
if exist "%M2_REPO%\org\springframework\boot\spring-boot\3.3.10\spring-boot-3.3.10.jar" (
    jar xf "%M2_REPO%\org\springframework\boot\spring-boot\3.3.10\spring-boot-3.3.10.jar"
)
if exist "%M2_REPO%\org\springframework\spring-context\6.1.13\spring-context-6.1.13.jar" (
    jar xf "%M2_REPO%\org\springframework\spring-context\6.1.13\spring-context-6.1.13.jar"
)
if exist "%M2_REPO%\org\springframework\spring-core\6.1.13\spring-core-6.1.13.jar" (
    jar xf "%M2_REPO%\org\springframework\spring-core\6.1.13\spring-core-6.1.13.jar"
)
if exist "%M2_REPO%\org\springframework\spring-beans\6.1.13\spring-beans-6.1.13.jar" (
    jar xf "%M2_REPO%\org\springframework\spring-beans\6.1.13\spring-beans-6.1.13.jar"
)

REM Common dependencies
if exist "%M2_REPO%\vn\com\mbbank\kanban\mbmonitor\common\mbmonitor-common\1.0-SNAPSHOT\mbmonitor-common-1.0-SNAPSHOT.jar" (
    jar xf "%M2_REPO%\vn\com\mbbank\kanban\mbmonitor\common\mbmonitor-common\1.0-SNAPSHOT\mbmonitor-common-1.0-SNAPSHOT.jar"
)

echo Creating new MANIFEST.MF...
echo Manifest-Version: 1.0 > META-INF\MANIFEST.MF
echo Main-Class: vn.com.mbbank.kanban.mbmonitor.external.job.Main >> META-INF\MANIFEST.MF
echo. >> META-INF\MANIFEST.MF

echo Creating fat JAR...
jar cfm ..\%FAT_JAR% META-INF\MANIFEST.MF *

cd ..
rmdir /s /q %TEMP_DIR%

echo.
echo Fat JAR created: %FAT_JAR%
echo You can now run: java -jar %FAT_JAR%
echo.
pause
