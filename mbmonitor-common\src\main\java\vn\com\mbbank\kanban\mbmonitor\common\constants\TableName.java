package vn.com.mbbank.kanban.mbmonitor.common.constants;

/**
 * TableName.
 */
public class TableName {
  private TableName() {
  }

  public static final String ALERT = "ALERT";
  public static final String ALERT_PRIORITY_CONFIG = "ALERT_PRIORITY_CONFIG";
  public static final String APPLICATION = "APPLICATION";
  public static final String CUSTOM_OBJECT = "CUSTOM_OBJECT";
  public static final String NOTE = "NOTE";
  public static final String PRIORITY_RAW_VALUE_CONFIG = "PRIORITY_RAW_VALUE_CONFIG";
  public static final String SERVICE = "SERVICE";
  public static final String SYS_USER = "SYS_USER";
  public static final String SYS_ROLE = "SYS_ROLE";
  public static final String SYS_USER_ROLE = "SYS_USER_ROLE";
  public static final String WEBHOOK = "WEBHOOK";
  public static final String EMAIL_PARTNER = "EMAIL_PARTNER";
  public static final String EMAIL_CONFIG = "EMAIL_CONFIG";
  public static final String EMAIL_PARTNER_ADDRESS = "EMAIL_PARTNER_ADDRESS";
  public static final String EMAIL_TEMPLATE = "EMAIL_TEMPLATE";
  public static final String EMAIL_TEMPLATE_RECEIVER = "EMAIL_TEMPLATE_RECEIVER";
  public static final String FILE_STORAGE = "FILE_STORAGE";
  public static final String DATABASE_CONNECTION = "DATABASE_CONNECTION";
  public static final String ALERT_GROUP = "ALERT_GROUP";
  public static final String ALERT_GROUP_CONFIG = "ALERT_GROUP_CONFIG";
  public static final String ALERT_GROUP_CONFIG_CONDITION = "ALERT_GROUP_CONFIG_CONDITION";
  public static final String ALERT_GROUP_CONFIG_CONDITION_VALUE =
      "ALERT_GROUP_CONFIG_CONDITION_VALUE";
  public static final String COLLECT_EMAIL_CONFIG = "COLLECT_EMAIL_CONFIG";
  public static final String ALERT_GROUP_CONFIG_DEPENDENCY = "ALERT_GROUP_CONFIG_DEPENDENCY";
  public static final String JOB_HISTORY = "JOB_HISTORY";
  public static final String JOB_HISTORY_DETAIL = "JOB_HISTORY_DETAIL";

  public static final String MAINTENANCE_TIME_CONFIG = "MAINTENANCE_TIME_CONFIG";
  public static final String MAINTENANCE_TIME_CONFIG_DEPENDENCY = "MAINTENANCE_TIME_CONFIG_DEPENDENCY";
  public static final String SYS_ROLE_PERMISSION = "SYS_ROLE_PERMISSION";
  public static final String TASK = "TASK";
  public static final String TASK_REFERENCE = "TASK_REFERENCE";
  public static final String TASK_USER = "TASK_USER";

  public static final String FILTER_ALERT_CONFIG = "FILTER_ALERT_CONFIG";
  public static final String FILTER_ALERT_CONFIG_DEPENDENCY = "FILTER_ALERT_CONFIG_DEPENDENCY";
  public static final String MODIFY_ALERT_CONFIG = "MODIFY_ALERT_CONFIG";
  public static final String MODIFY_ALERT_CONFIG_DEPENDENCY = "MODIFY_ALERT_CONFIG_DEPENDENCY";
  public static final String MODIFY_ALERT_CONFIG_MODIFY = "MODIFY_ALERT_CONFIG_MODIFY";
  public static final String FILTER_ALERT_CONFIG_SOURCE = "FILTER_ALERT_CONFIG_SOURCE";

  public static final String TELEGRAM_ALERT_CONFIG = "TELEGRAM_ALERT_CONFIG";
  public static final String TELEGRAM_CONFIG = "TELEGRAM_CONFIG";

  public static final String SYS_LOG = "SYS_LOG";
  public static final String SYS_USER_REFRESH_TOKEN = "SYS_USER_REFRESH_TOKEN";
  public static final String EXECUTION = "EXECUTION";
  public static final String EXECUTION_GROUP = "EXECUTION_GROUP";
  public static final String EXECUTION_HISTORY = "EXECUTION_HISTORY";
  public static final String EXECUTION_PARAM = "EXECUTION_PARAM";
  public static final String VARIABLE = "VARIABLE";
  public static final String AUTO_TRIGGER_ACTION_CONFIG_DEPENDENCY = "AUTO_TRIGGER_ACTION_CONFIG_DEPENDENCY";
  public static final String AUTO_TRIGGER_ACTION_CONFIG = "AUTO_TRIGGER_ACTION_CONFIG";
  public static final String AUTO_TRIGGER_ACTION_CONFIG_EXECUTION_MAPPER =
          "AUTO_TRIGGER_ACTION_CONFIG_EXECUTION_MAPPER";
  public static final String NOTIFICATION = "NOTIFICATION";
  public static final String MONITOR_ACTION = "MONITOR_ACTION";
  public static final String MONITOR_WEB_CONFIG = "MONITOR_WEB_CONFIG";
  public static final String RPA_CONFIG = "RPA_CONFIG";
  public static final String NOTIFICATION_EVENT = "NOTIFICATION_EVENT";
  public static final String NOTIFICATION_EVENT_TARGET = "NOTIFICATION_EVENT_TARGET";
  public static final String ALERT_REQUEST = "ALERT_REQUEST";
  public static final String ALERT_REQUEST_DATABASE = "ALERT_REQUEST_DATABASE";
}
