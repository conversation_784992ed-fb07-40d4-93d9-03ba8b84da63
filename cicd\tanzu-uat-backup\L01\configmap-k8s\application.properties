server.port=9006
server.servlet.context-path=/api/external-job
#Metric
management.server.port=9006
management.endpoint.shutdown.enabled=true
management.endpoints.web.exposure.include=health,metrics,prometheus
management.endpoint.health.show-details=always
spring.jpa.properties.hibernate.format_sql=true
management.endpoint.health.probes.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.endpoint.health.group.liveness.include=livenessState,ping,diskSpace
management.endpoint.health.group.readiness.include=readinessState,ping
#database config
spring.datasource.url=********************************************
spring.datasource.hikari.username=mbmonitor
spring.datasource.hikari.password=ENC(8rpDcv8NUgU2AiaFjmMxvJp0wd3hKYw1)
#datasource.oracle.password=Mb123456
#spring.datasource.hikari.password=ENC(xnVJLFY924oxrGb9bT6mhHsGsR5cYpn1)
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=50
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000
spring.jackson.serialization.fail-on-empty-beans=false
spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver
spring.jpa.properties.hibernate.jdbc.batch_size=600
spring.main.allow-bean-definition-overriding=true
# swagger config
springdoc.swagger-ui.enabled=false
springdoc.api-docs.enabled=false
# -------------------------------
# Jasypt Encrypt config
# -------------------------------
jasypt.encryptor.bean=encryptorBean
jasypt.encryptor.algorithm=PBEWithMD5AndDES
jasypt.encryptor.keyobtentioniterations=1000
jasypt.encryptor.poolsize=1
jasypt.encryptor.providername=SunJCE
jasypt.encryptor.saltgeneratorclassname=org.jasypt.salt.RandomSaltGenerator
jasypt.encryptor.stringoutputtype=base64
# -------------------------------
#keycloak
kanban.keycloak.client-id=mbmonitor-server
kanban.keycloak.client-secret=ENC(MUBGXD1CAI114nmhFPx/MpuLorWDJWcKyBto3f/D679zK1ibSzDGpPw4bGgWaxrx)
kanban.keycloak.introspect-uri=https://keycloak-internal-uat.mbbank.com.vn/auth/realms/internal/protocol/openid-connect/token/introspect
#redis config
kanban.redis.url=10.215.254.135:6379
kanban.redis.password=ENC(DpX7M3Dz57PT0tfqY8WJwRa4d+QAvchN)
#kafka config
kanban.kafka.bootstrap-server=10.215.109.201:9093,10.215.109.205:9093,10.215.109.206:9093,10.215.109.207:9093,10.215.109.208:9093,**************:9093
kanban.kafka.producer.timeout=600
kanban.kafka.consumer.auto-commit=false
kanban.kafka.consumer.group-id=mbmonitor
kanban.kafka.consumer.retry=5
kanban.kafka.consumer.retry-backoff=60000
kanban.kafka.consumer.ack-mode=MANUAL
kanban.kafka.producer.max-request-size=100000
kanban.kafka.topic.jobs=APP_MBMONITOR_JOBS
kanban.kafka.username=user04
kanban.kafka.password=ENC(dxrdcxoHwanPyvzfYFjsBS91etaT1Kive/nGA16vaws=)
kanban.kafka.key=emailConfigId
kanban.general.white-list.origin=http://localhost:8400,http://localhost:9000,http://localhost:9990,http://localhost:9007,http://mbmonitor.tanzu-dev.mbbank.com.vn

#keycloak centrailized
kanban.authentication.keycloak.centrailized.url=http://**********:8831/auth/realms/ms-core/protocol/openid-connect/token
kanban.authentication.keycloak.centrailized.username=mbmonitor_user
kanban.authentication.keycloak.centrailized.password=fb7bfb60-f5e3-4f8d-b4ad-3f22249b1fa0
kanban.authentication.keycloak.centrailized.public-key=-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3sROnOw4rj3ainkFaVHf05ha3yt4njOxJzljdebkJJckOXj1wag47nGSukVcsytXuhq8QrM6z8KpcSsAXNYNBgM7i7/X+ZxTf/b5cDKDpwiOzcWbMazii5/8ljIII9Z2sBOXQAf3Ev2NIgUlXBOuwhkQ9gFWmni+GvLnQxNVdJ/16ABelL1LCAbnDlX0VrhLnMQUihjf0Npy+jv5vm20DMzpk5rTo01XTTirrUmiaE76qILbwILqZDVwd4RlnGWD37N9qbjYTZ6AQ3s/yCaEgeJ0z4kzqP5G2oXvll9joV5P/Jub7WGRh9alj+VfhYp9MusTzlLJmqb8uvY3w7E67QIDAQAB-----END PUBLIC KEY-----
kanban.authentication.keycloak.centrailized.enable=false

# thread-pool config
thread-pool.pool-size.core=20
thread-pool.pool-size.max=50
thread-pool.queue-capacity=500
thread-pool.thread-name-prefix=Request-
thread-pool.await-termination-seconds=120
monitor.sql.query.row.max=500
monitor.redis.lock.default.timeout=10000
monitor.redis.lock.email.timeout=600000
monitor.redis.lock.database.timeout=60000
# 30 minute
mbmonitor.alertGroup.alertGroupHandleTriggerInterval=1800
mbmonitor.alertGroup.amountOfAlertProcessAtTheSameTime=300
#Telegram
mbmonitor.telegram.local.dns.ip=***********
mbmonitor.telegram.local.dns.port=443
mbmonitor.telegram.alert.send.limit.seconds=600
# setting hikari query sql
mbmonitor.database.hikari.minimumIdle=5
mbmonitor.database.hikari.maximumPoolSize=20
mbmonitor.database.hikari.idleTimeout=30000
mbmonitor.database.hikari.poolName=monitorCP
mbmonitor.database.hikari.maxLifetime=2000000
mbmonitor.database.hikari.connectionTimeout=30000
#trust domain
mbmonitor.trusted.hostnames=mbmonitor.tanzu-uat.mbbank.com.vn

monitor.external.execution.url=https://mbmonitor.tanzu-uat.mbbank.com.vn/api/external-execution/

# path variable for playwright
mbmonitor.monitor.web.executable.path.chromium=/data/mbmonitor/ms-playwright/chromium-1178/chrome-linux/chrome
mbmonitor.monitor.web.executable.path.firefox=/data/mbmonitor/ms-playwright/firefox-1487/firefox/firefox
mbmonitor.monitor.web.executable.path.driver=/data/mbmonitor/ms-playwright/driver

